<?php
// generate_plan.php
session_start();
require_once 'config.php'; // ملف اتصال قاعدة البيانات
function callGoogleAI($prompt) {
    $apiKey = 'AIzaSyBLX9Pkgdbtm2Oitq7TTgZR7ONM9CMRgdQ';
    $url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';
    
    $data = [
        'contents' => [
            ['parts' => [['text' => $prompt]]]
        ]
    ];

    $ch = curl_init($url . '?key=' . $apiKey);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);

    $response = curl_exec($ch);
    curl_close($ch);

    return json_decode($response, true);
}

function saveTourPlan($db, $tourData, $preferences, $planDetails) {
    try {
        // تحضير البيانات للإدراج
        $city = $tourData['city'];
        $days = $preferences['days'];
        $travelersCount = $preferences['travelers_count'];
        $accommodation = implode(', ', $preferences['accommodation']);
        $restaurants = implode(', ', $preferences['restaurants']);
        $activities = implode(', ', $preferences['activities']);
        $budget = $preferences['budget'];

        // حساب تاريخ بداية ونهاية الرحلة
        $startDate = date('Y-m-d'); // تاريخ اليوم
        $endDate = date('Y-m-d', strtotime("+{$days} days"));

        // إعداد استعلام SQL
        $stmt = $db->prepare("INSERT INTO tour_plans (
            user_id, city, start_date, end_date, days_count, 
            travelers_count, accommodation, restaurants, 
            activities, total_budget, plan_details
        ) VALUES (
            :user_id, :city, :start_date, :end_date, :days_count, 
            :travelers_count, :accommodation, :restaurants, 
            :activities, :total_budget, :plan_details
        )");

        // ربط القيم
        $stmt->bindValue(':user_id', $_SESSION['user_id'] ?? 0);
        $stmt->bindValue(':city', $city);
        $stmt->bindValue(':start_date', $startDate);
        $stmt->bindValue(':end_date', $endDate);
        $stmt->bindValue(':days_count', $days);
        $stmt->bindValue(':travelers_count', $travelersCount);
        $stmt->bindValue(':accommodation', $accommodation);
        $stmt->bindValue(':restaurants', $restaurants);
        $stmt->bindValue(':activities', $activities);
        $stmt->bindValue(':total_budget', str_replace(['منخفضة', 'متوسطة', 'مرتفعة', 'فاخرة'], [1000, 3000, 7000, 10000], $budget));
        $stmt->bindValue(':plan_details', $planDetails);

        // تنفيذ الاستعلام
        $stmt->execute();

        return $db->lastInsertId(); // إرجاع رقم السجل المدرج
    } catch(PDOException $e) {
        // التسجيل أو طباعة الخطأ
        error_log("خطأ في حفظ البرنامج السياحي: " . $e->getMessage());
        return false;
    }
}

// في نفس الملف بعد توليد الخطة
if (isset($response['candidates'][0]['content']['parts'][0]['text'])) {
    $itinerary = $response['candidates'][0]['content']['parts'][0]['text'];
    
    // حفظ الخطة في قاعدة البيانات
    $planId = saveTourPlan($db, $tourData, $preferences, $itinerary);
    
    // يمكنك إضافة معرف الخطة في جلسة المستخدم إذا أردت
    $_SESSION['current_plan_id'] = $planId;
}

// Combine all preferences into a detailed prompt
$tourData = $_SESSION['tour_data'];
$preferences = $_POST;

// بناء البرومبت بشكل أكثر تفصيلاً
$prompt = "أنا مسافر إلى {$tourData['city']} مع " . 
          ($preferences['travelers_count'] > 1 ? "{$preferences['travelers_count']} أشخاص" : "نفسي") . 
          " لمدة {$preferences['days']} أيام.\n";

// تفاصيل السكن
$prompt .= "أرغب بالإقامة في: " . implode(" أو ", $preferences['accommodation']) . 
           " مع إطلالة " . implode(" أو ", $preferences['accommodation_view'] ?? []) . ".\n";

// تفضيلات الطعام
$prompt .= "تفضيلاتي الغذائية:\n";
$prompt .= "- أنواع المطاعم: " . implode(" و ", $preferences['restaurants']) . "\n";
$prompt .= "- نوع الطعام: " . implode(" و ", $preferences['food_type'] ?? []) . "\n";
$prompt .= "- الوجبات المفضلة: " . implode(" و ", $preferences['meals']) . "\n";

// الأنشطة والترفيه
$prompt .= "الأنشطة المطلوبة:\n";
$prompt .= "- أنشطة سياحية: " . implode(" و ", $preferences['activities']) . "\n";
$prompt .= "- أماكن ترفيهية: " . implode(" و ", $preferences['entertainment'] ?? []) . "\n";

// وسائل التنقل
$prompt .= "وسائل النقل: " . implode(" و ", $preferences['transport']) . 
           " مع سيارة " . ($preferences['car_type'] ?? 'اقتصادية') . ".\n";

// الميزانية
$prompt .= "الميزانية: {$preferences['budget']} مع تفضيلات إنفاق " . 
           implode(" و ", $preferences['spending_preferences'] ?? []) . ".\n";

// متطلبات خاصة
if (!empty($preferences['special_needs'])) {
    $prompt .= "متطلبات خاصة: " . implode(" و ", $preferences['special_needs']) . ".\n";
}

// ملاحظات إضافية
if (!empty($preferences['additional_notes'])) {
    $prompt .= "ملاحظات إضافية: {$preferences['additional_notes']}\n";
}

// التعليمات النهائية للذكاء الاصطناعي
$prompt .= "\nأريدك أن تقوم بما يلي:
1. إنشاء برنامج سياحي مفصل يومياً
2. تحديد الأوقات بدقة لكل نشاط
3. إضافة روابط Google Maps لكل معلم أو مطعم أو نشاط
4. تقدير التكلفة التقريبية لكل نشاط والإقامة
5. مراعاة التفضيلات والمتطلبات الخاصة
6. أوقات الافتتاح  او الاغلاق  والزحام والذروة ان وجد من قوقل ماب
7.التقييم من قبل الزوار  ان تيسر 
8.نبذه بسيطة أو تعريفية عن المكان
9. التأكد من تناسب البرنامج مع عدد الأشخاص والميزانية";

$response = callGoogleAI($prompt);
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>البرنامج السياحي المقترح</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .itinerary-day { margin-bottom: 30px; border: 1px solid #ddd; padding: 20px; border-radius: 8px; }
        .day-header { background: #f8f9fa; padding: 10px; margin-bottom: 15px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container my-5">
        <h2 class="text-center mb-4">البرنامج السياحي المقترح</h2>
        <div class="itinerary-container">
            <?php
            if (isset($response['candidates'][0]['content']['parts'][0]['text'])) {
                $itinerary = $response['candidates'][0]['content']['parts'][0]['text'];
                
                // Format and display the itinerary
                $days = explode("\n\n", $itinerary);
                foreach ($days as $day) {
                    if (!empty(trim($day))) {
                        echo '<div class="itinerary-day">';
                        $lines = explode("\n", $day);
                        echo '<div class="day-header">' . array_shift($lines) . '</div>';
                        foreach ($lines as $line) {
                            echo '<p>' . $line . '</p>';
                        }
                        echo '</div>';
                    }
                }
            } else {
                echo '<div class="alert alert-warning">عذراً، حدث خطأ في إنشاء البرنامج. يرجى المحاولة مرة أخرى.</div>';
            }
            ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="index.php" class="btn btn-primary">إنشاء برنامج جديد</a>
            <button onclick="window.print()" class="btn btn-secondary">طباعة البرنامج</button>
        </div>
    </div>
</body>
</html>
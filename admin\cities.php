<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once '../config.php';
require_once 'auth_check.php';

// Add at top after session start and requires
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_city'])) {
    try {
        // Validate inputs
        $cityName = trim($_POST['city_name']);
        $countryId = (int)$_POST['country_id'];
        
        if (empty($cityName) || empty($countryId)) {
            throw new Exception('جميع الحقول مطلوبة');
        }

        // Check if city already exists
        $checkStmt = $db->prepare("SELECT COUNT(*) FROM cities WHERE name = ? AND country_id = ?");
        $checkStmt->execute([$cityName, $countryId]);
        if ($checkStmt->fetchColumn() > 0) {
            throw new Exception('هذه المدينة موجودة مسبقاً');
        }

        // Insert new city
        $stmt = $db->prepare("INSERT INTO cities (name, country_id) VALUES (?, ?)");
        $stmt->execute([$cityName, $countryId]);
        
        $_SESSION['success'] = 'تمت إضافة المدينة بنجاح';
        header('Location: cities.php');
        exit;

    } catch (Exception $e) {
        $_SESSION['error'] = $e->getMessage();
    }
}

// Add edit handler at top of file
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['edit_city'])) {
    try {
        $cityId = (int)$_POST['city_id'];
        $cityName = trim($_POST['city_name']);
        $countryId = (int)$_POST['country_id'];
        
        if (empty($cityName) || empty($countryId)) {
            throw new Exception('جميع الحقول مطلوبة');
        }

        $stmt = $db->prepare("UPDATE cities SET name = ?, country_id = ? WHERE id = ?");
        $stmt->execute([$cityName, $countryId, $cityId]);
        
        $_SESSION['success'] = 'تم تحديث المدينة بنجاح';
        header('Location: cities.php');
        exit;
    } catch (Exception $e) {
        $_SESSION['error'] = $e->getMessage();
    }
}

// Handle city deletion
if (isset($_POST['delete_city'])) {
    try {
        $stmt = $db->prepare("DELETE FROM cities WHERE id = ?");
        $stmt->execute([$_POST['city_id']]);
        $_SESSION['success'] = 'تم حذف المدينة بنجاح';
    } catch(PDOException $e) {
        $_SESSION['error'] = 'حدث خطأ أثناء حذف المدينة';
    }
}

// Get cities with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Add search parameter
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

try {
    // Update count query with search
    $countStmt = $db->prepare("
        SELECT COUNT(*) 
        FROM cities c
        LEFT JOIN countries co ON c.country_id = co.id
        WHERE c.name LIKE ? OR co.name LIKE ?
    ");
    $searchParam = "%$search%";
    $countStmt->execute([$searchParam, $searchParam]);
    $total = $countStmt->fetchColumn();
    $totalPages = ceil($total / $limit);
    
    // Update main query with search
    $stmt = $db->prepare("
        SELECT 
            c.id,
            c.name as city_name,
            co.name as country_name,
            COUNT(f.id) as total_favorites
        FROM cities c
        LEFT JOIN countries co ON c.country_id = co.id
        LEFT JOIN favorites f ON c.name = f.city_name
        WHERE c.name LIKE ? OR co.name LIKE ?
        GROUP BY c.id, c.name, co.name
        ORDER BY c.name
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$searchParam, $searchParam, $limit, $offset]);
    $cities = $stmt->fetchAll();

    // Get countries for dropdown
    $countriesStmt = $db->query("SELECT id, name FROM countries ORDER BY name");
    $countries = $countriesStmt->fetchAll();

} catch(PDOException $e) {
    error_log("Query error: " . $e->getMessage());
    $_SESSION['error'] = 'حدث خطأ في جلب البيانات';
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المدن</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/css/admin.css" rel="stylesheet">
</head>
<?php include '../navbar.php'; ?>
<body>
    <div class="d-flex">
        <?php include 'sidebar.php'; ?>

        <div class="main-content p-4 w-100">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>إدارة المدن</h2>
                <!-- Update search input -->
                <div class="d-flex gap-2">
                    <form class="d-flex" method="GET">
                        <input type="text" class="form-control me-2" name="search" 
                               value="<?= htmlspecialchars($search) ?>" 
                               placeholder="بحث عن مدينة أو دولة...">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i>
                        </button>
                    </form>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCityModal">
                        <i class="bi bi-plus-lg"></i> إضافة مدينة
                    </button>
                </div>
            </div>

            <?php if(isset($_SESSION['success'])): ?>
                <div class="alert alert-success"><?= $_SESSION['success']; unset($_SESSION['success']); ?></div>
            <?php endif; ?>

            <?php if(isset($_SESSION['error'])): ?>
                <div class="alert alert-danger"><?= $_SESSION['error']; unset($_SESSION['error']); ?></div>
            <?php endif; ?>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم المدينة</th>
                                    <th>البرامج السياحية</th>
                                    <th>البلد</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($cities as $city): ?>
                                <tr>
                                    <td><?= htmlspecialchars($city['id']) ?></td>
                                    <td><?= htmlspecialchars($city['city_name']) ?></td>
                                    <td><?= (int)$city['total_favorites'] ?></td>
                                    <td><?= htmlspecialchars($city['country_name']) ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editCity(<?= $city['id'] ?>)">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <form action="" method="POST" class="d-inline" 
                                              onsubmit="return confirm('هل أنت متأكد من حذف هذه المدينة؟')">
                                            <input type="hidden" name="city_id" value="<?= $city['id'] ?>">
                                            <button type="submit" name="delete_city" class="btn btn-sm btn-danger">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <?php if($totalPages > 1): ?>
                    <nav class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php for($i = 1; $i <= $totalPages; $i++): ?>
                                <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=<?= $i ?>"><?= $i ?></a>
                                </li>
                            <?php endfor; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Add City Modal -->
    <div class="modal fade" id="addCityModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مدينة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form action="" method="POST">
                        <div class="mb-3">
                            <label class="form-label">اسم المدينة</label>
                            <input type="text" class="form-control" name="city_name" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الدولة</label>
                            <select class="form-select" name="country_id" required>
                                <option value="">اختر الدولة</option>
                                <?php foreach($countries as $country): ?>
                                    <option value="<?= $country['id'] ?>"><?= htmlspecialchars($country['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="text-end">
                            <button type="submit" name="add_city" class="btn btn-primary">حفظ</button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit City Modal -->
    <div class="modal fade" id="editCityModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل المدينة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form action="" method="POST">
                        <input type="hidden" name="city_id" id="edit_city_id">
                        <div class="mb-3">
                            <label class="form-label">اسم المدينة</label>
                            <input type="text" class="form-control" name="city_name" id="edit_city_name" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الدولة</label>
                            <select class="form-select" name="country_id" id="edit_country_id" required>
                                <option value="">اختر الدولة</option>
                                <?php foreach($countries as $country): ?>
                                    <option value="<?= $country['id'] ?>"><?= htmlspecialchars($country['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="text-end">
                            <button type="submit" name="edit_city" class="btn btn-primary">حفظ التغييرات</button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    function editCity(cityId) {
        fetch('get_city.php?id=' + cityId)
            .then(response => response.json())
            .then(data => {
                document.getElementById('edit_city_id').value = data.id;
                document.getElementById('edit_city_name').value = data.name;
                document.getElementById('edit_country_id').value = data.country_id;
                
                new bootstrap.Modal(document.getElementById('editCityModal')).show();
            })
            .catch(error => console.error('Error:', error));
    }
    </script>
</body>
</html>
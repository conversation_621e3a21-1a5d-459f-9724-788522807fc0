<?php
// index.php
session_start();
require_once 'config.php';
require_once 'auth_middleware.php';
requireLogin();
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>برنامج السياحة العالمية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: 'Arial', sans-serif; 
            background-color: #f8f9fa;
            padding: 20px; 
        }
        .form-container { 
            max-width: 800px; 
            margin: 20px auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .form-select, .btn {
            margin-bottom: 15px;
        }
    </style>
</head>
<nav class="navbar navbar-expand-lg navbar-light bg-light">
    <div class="container">
        <a class="navbar-brand" href="index.php">مخطط الرحلات</a>
        <div class="navbar-nav">
            <?php if(isset($_SESSION['user_id'])): ?>
                <span class="navbar-text me-3">
                    مرحباً، <?= htmlspecialchars($_SESSION['username']) ?>
                </span>
                <a class="nav-item nav-link" href="preferences.php">إنشاء رحلة جديدة</a>
                <a class="nav-item nav-link" href="my_tours.php">رحلاتي السابقة</a>
                <a class="nav-item nav-link" href="login.php?logout=1">تسجيل الخروج</a>
            <?php else: ?>
                <a class="nav-item nav-link" href="login.php">تسجيل الدخول</a>
                <a class="nav-item nav-link" href="register.php">إنشاء حساب</a>
            <?php endif; ?>
        </div>
    </div>
</nav>
<body>
    <div class="container form-container">
        <h2 class="text-center mb-4">برنامج السياحة العالمية</h2>
        
        <form id="tourForm" action="preferences.php" method="POST">
            <div class="mb-3">
                <label class="form-label">اختر الدولة:</label>
                <select class="form-select" name="country" id="countrySelect" required>
                    <option value="">اختر الدولة</option>
                    <?php
                    $result = mysqli_query($conn, "SELECT * FROM countries ORDER BY name");
                    while($row = mysqli_fetch_assoc($result)) {
                        echo "<option value='".$row['id']."'>".$row['name']."</option>";
                    }
                    ?>
                </select>
            </div>

            <div class="mb-3">
                <label class="form-label">اختر المدينة:</label>
                <select class="form-select" name="city" id="citySelect" required>
                    <option value="">اختر المدينة أولاً</option>
                </select>
            </div>

            <div class="mb-3">
                <label class="form-label">عدد المسافرين:</label>
                <select class="form-select" name="travelers" required>
                    <?php for($i=1; $i<=10; $i++) { ?>
                        <option value="<?php echo $i; ?>"><?php echo $i; ?> مسافر</option>
                    <?php } ?>
                </select>
            </div>

            <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg">عمل برنامج سياحي</button>
            </div>
        </form>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $('#countrySelect').change(function() {
            var countryId = $(this).val();
            if(countryId) {
                $.ajax({
                    url: 'get_cities.php',
                    type: 'POST',
                    data: {country_id: countryId},
                    success: function(response) {
                        $('#citySelect').html(response);
                    },
                    error: function() {
                        alert('حدث خطأ في تحميل المدن');
                    }
                });
            } else {
                $('#citySelect').html('<option value="">اختر المدينة أولاً</option>');
            }
        });
    </script>
</body>
</html>
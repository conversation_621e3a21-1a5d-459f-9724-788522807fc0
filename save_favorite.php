<?php
session_start();
require_once 'config.php';

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['error' => 'User not logged in']);
    exit;
}

$data = json_decode(file_get_contents('php://input'), true);
$suggestion = $data['suggestion'] ?? '';
$city = $data['city'] ?? '';

if (empty($suggestion) || empty($city)) {
    echo json_encode(['error' => 'Missing data']);
    exit;
}

try {
    $stmt = $db->prepare("INSERT INTO favorites (user_id, city_name, suggestion_text) VALUES (?, ?, ?)");
    $stmt->execute([$_SESSION['user_id'], $city, $suggestion]);
    echo json_encode(['success' => true]);
} catch(PDOException $e) {
    echo json_encode(['error' => $e->getMessage()]);
}
?>
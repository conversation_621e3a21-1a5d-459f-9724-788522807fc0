<?php
session_start();
require_once 'config.php';

if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $favorites_privacy = isset($_POST['favorites_privacy']) ? intval($_POST['favorites_privacy']) : 0;
    
    try {
        $stmt = $db->prepare("UPDATE users SET favorites_privacy = ? WHERE id = ?");
        $stmt->execute([$favorites_privacy, $_SESSION['user_id']]);
        $_SESSION['success_message'] = 'تم تحديث الإعدادات بنجاح';
    } catch(PDOException $e) {
        $_SESSION['error_message'] = 'حدث خطأ أثناء تحديث الإعدادات';
    }
    
    header('Location: settings.php');
    exit;
}
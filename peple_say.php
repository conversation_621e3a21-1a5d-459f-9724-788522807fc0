<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once 'config.php';
require_once 'session_handler.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_testimonial'])) {
    try {
        $name = trim($_POST['name']);
        $city = trim($_POST['city']);
        $content = trim($_POST['content']);
        
        if (empty($name) || empty($city) || empty($content)) {
            throw new Exception('جميع الحقول مطلوبة');
        }

        $stmt = $db->prepare("INSERT INTO testimonials (name, city, content, status) VALUES (?, ?, ?, 'pending')");
        $stmt->execute([$name, $city, $content]);
        
        $_SESSION['success'] = 'تمت إضافة الرأي بنجاح';
        header('Location: peple_say.php');
        exit;
    } catch (Exception $e) {
        $_SESSION['error'] = $e->getMessage();
    }
}

// Handle status updates
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_status'])) {
    $id = (int)$_POST['testimonial_id'];
    $status = $_POST['status'];
    $stmt = $db->prepare("UPDATE testimonials SET status = ? WHERE id = ?");
    $stmt->execute([$status, $id]);
    $_SESSION['success'] = 'تم تحديث الحالة بنجاح';
    header('Location: testimonials.php');
    exit;
}

// Get testimonials with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

try {
    $total = $db->query("SELECT COUNT(*) FROM testimonials")->fetchColumn();
    $totalPages = ceil($total / $limit);
    
    $stmt = $db->prepare("
        SELECT * FROM testimonials 
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$limit, $offset]);
    $testimonials = $stmt->fetchAll();
} catch(PDOException $e) {
    error_log("Error fetching testimonials: " . $e->getMessage());
    $_SESSION['error'] = 'حدث خطأ في جلب البيانات';
    $testimonials = [];
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة آراء الزوار</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/css/admin.css" rel="stylesheet">
</head>
<?php include 'navbar.php'; ?>
<body>
    <div class="d-flex">
       
        
        <div class="main-content p-4 w-100">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>إدارة آراء الزوار</h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTestimonialModal">
                    <i class="bi bi-plus-lg"></i> إضافة رأي جديد
                </button>
            </div>

            <?php if(isset($_SESSION['success'])): ?>
                <div class="alert alert-success"><?= $_SESSION['success']; unset($_SESSION['success']); ?></div>
            <?php endif; ?>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>المدينة</th>
                                    <th>المحتوى</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($testimonials as $item): ?>
                                <tr>
                                    <td><?= htmlspecialchars($item['name']) ?></td>
                                    <td><?= htmlspecialchars($item['city']) ?></td>
                                    <td><?= htmlspecialchars($item['content']) ?></td>
                                    <td>
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="testimonial_id" value="<?= $item['id'] ?>">
                                            <select name="status" onchange="this.form.submit()" class="form-select form-select-sm">
                                                <option value="pending" <?= $item['status'] == 'pending' ? 'selected' : '' ?>>قيد المراجعة</option>
                                                <option value="approved" <?= $item['status'] == 'approved' ? 'selected' : '' ?>>مقبول</option>
                                                <option value="rejected" <?= $item['status'] == 'rejected' ? 'selected' : '' ?>>مرفوض</option>
                                            </select>
                                            <input type="hidden" name="update_status" value="1">
                                        </form>
                                    </td>
                                    <td><?= date('Y/m/d', strtotime($item['created_at'])) ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-danger" onclick="deleteTestimonial(<?= $item['id'] ?>)">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <?php if($totalPages > 1): ?>
                    <nav class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php for($i = 1; $i <= $totalPages; $i++): ?>
                                <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=<?= $i ?>"><?= $i ?></a>
                                </li>
                            <?php endfor; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="addTestimonialModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة رأي جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">الاسم</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">المدينة</label>
                            <input type="text" class="form-control" name="city" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">المحتوى</label>
                            <textarea class="form-control" name="content" rows="4" required></textarea>
                        </div>
                        <div class="text-end">
                            <button type="submit" name="add_testimonial" class="btn btn-primary">حفظ</button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
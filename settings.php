<?php
session_start();
require_once 'config.php';
require_once 'auth_middleware.php';
requireLogin();
// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Check for column existence and add if missing
try {
    $stmt = $db->query("SHOW COLUMNS FROM users LIKE 'favorites_privacy'");
    if ($stmt->rowCount() == 0) {
        $db->exec("ALTER TABLE users ADD favorites_privacy TINYINT(1) NOT NULL DEFAULT 0");
    }
} catch(PDOException $e) {
    error_log("Column check error: " . $e->getMessage());
}

// إنشاء جدول user_settings إذا لم يكن موجوداً
try {
    $db->exec("CREATE TABLE IF NOT EXISTS user_settings (
        user_id INT PRIMARY KEY,
        email_notifications TINYINT(1) DEFAULT 1,
        sms_notifications TINYINT(1) DEFAULT 0,
        profile_visibility VARCHAR(20) DEFAULT 'private',
        theme_preference VARCHAR(20) DEFAULT 'light'
    )");
} catch(PDOException $e) {
    error_log("خطأ في إنشاء الجدول: " . $e->getMessage());
}


// Load user data
try {
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    $_SESSION['error_message'] = 'حدث خطأ في جلب البيانات';
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // Handle privacy settings
        if (isset($_POST['favorites_privacy'])) {
            $favorites_privacy = intval($_POST['favorites_privacy']);
            
            $stmt = $db->prepare("UPDATE users SET favorites_privacy = ? WHERE id = ?");
            $result = $stmt->execute([$favorites_privacy, $_SESSION['user_id']]);
            
            if ($result) {
                $_SESSION['success_message'] = 'تم تحديث إعدادات الخصوصية بنجاح';
            } else {
                $_SESSION['error_message'] = 'لم يتم تحديث الإعدادات';
            }
            
            header('Location: settings.php');
            exit;
        }
        
        // معالجة إعدادات الإشعارات
        if (isset($_POST['notification_settings'])) {
            $email_notifications = isset($_POST['email_notifications']) ? 1 : 0;
            $sms_notifications = isset($_POST['sms_notifications']) ? 1 : 0;

            // محاولة التحديث أو الإدراج
            $stmt = $db->prepare("
                INSERT INTO user_settings 
                (user_id, email_notifications, sms_notifications) 
                VALUES (?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                email_notifications = ?, 
                sms_notifications = ?
            ");
            
            $result = $stmt->execute([
                $_SESSION['user_id'],
                $email_notifications,
                $sms_notifications,
                $email_notifications,
                $sms_notifications
            ]);

            $success_message = $result 
                ? "تم تحديث إعدادات الإشعارات بنجاح" 
                : "فشل تحديث الإعدادات";
        }
        
        // معالجة إعدادات الخصوصية
        if (isset($_POST['privacy_settings'])) {
            $profile_visibility = $_POST['profile_visibility'] ?? 'private';
            
            $stmt = $db->prepare("
                INSERT INTO user_settings 
                (user_id, profile_visibility) 
                VALUES (?, ?)
                ON DUPLICATE KEY UPDATE 
                profile_visibility = ?
            ");
            
            $result = $stmt->execute([
                $_SESSION['user_id'],
                $profile_visibility,
                $profile_visibility
            ]);

            $success_message = $result 
                ? "تم تحديث إعدادات الخصوصية بنجاح" 
                : "فشل تحديث إعدادات الخصوصية";
        }
        
        // معالجة إعدادات المظهر
        if (isset($_POST['theme_settings'])) {
            $theme = $_POST['theme'] ?? 'light';
            
            $stmt = $db->prepare("
                INSERT INTO user_settings 
                (user_id, theme_preference) 
                VALUES (?, ?)
                ON DUPLICATE KEY UPDATE 
                theme_preference = ?
            ");
            
            $result = $stmt->execute([
                $_SESSION['user_id'],
                $theme,
                $theme
            ]);

            $success_message = $result 
                ? "تم تحديث إعدادات المظهر بنجاح" 
                : "فشل تحديث إعدادات المظهر";
        }

    } catch(PDOException $e) {
        $error_message = "خطأ: " . $e->getMessage();
        error_log("خطأ في تحديث الإعدادات: " . $e->getMessage());
    }
}

// تعريف الإعدادات الافتراضية
$settings = [
    'email_notifications' => 1,
    'sms_notifications' => 0,
    'profile_visibility' => 'private',
    'theme_preference' => 'light'
];

$success_message = '';
$error_message = '';

// Load user data
try {
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        $_SESSION['error_message'] = 'لم يتم العثور على بيانات المستخدم';
        header('Location: index.php');
        exit;
    }
} catch(PDOException $e) {
    $_SESSION['error_message'] = 'حدث خطأ في جلب البيانات';
    header('Location: index.php');
    exit;
}

// جلب الإعدادات الحالية
try {
    // محاولة جلب الإعدادات من جدول user_settings
    $stmt = $db->prepare("
        SELECT 
            COALESCE(email_notifications, 1) as email_notifications, 
            COALESCE(sms_notifications, 0) as sms_notifications, 
            COALESCE(profile_visibility, 'private') as profile_visibility, 
            COALESCE(theme_preference, 'light') as theme_preference 
        FROM user_settings 
        WHERE user_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    
    $fetchedSettings = $stmt->fetch(PDO::FETCH_ASSOC);

    // إذا لم يتم العثور على إعدادات، استخدم الإعدادات الافتراضية
    if ($fetchedSettings) {
        $settings = array_merge($settings, $fetchedSettings);
    }

} catch(PDOException $e) {
    $error_message = "خطأ في جلب الإعدادات: " . $e->getMessage();
    error_log("خطأ في جلب الإعدادات: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>الإعدادات</title>
    
    <!-- Bootstrap 5.3 CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f4f6f9;
        }
        .settings-container {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            padding: 30px;
            margin-top: 30px;
        }
        .settings-section {
            margin-bottom: 30px;
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
        }
        .form-check-input:checked {
            background-color: #3498db;
            border-color: #3498db;
        }
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-top: 20px;
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <?php include 'navbar.php'; ?>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="settings-container">
                    <h2 class="text-center mb-4">
                        <i class="bi bi-gear me-2"></i>الإعدادات
                    </h2>

                    <!-- رسائل النجاح والخطأ -->
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i>
                            <?= htmlspecialchars($success_message) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <?= htmlspecialchars($error_message) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <!-- إعدادات الإشعارات -->
                    <div class="settings-section">
                        <h5>
                            <i class="bi bi-bell me-2 text-warning"></i>
                            إعدادات الإشعارات
                        </h5>
                        <form method="POST">
                            <input type="hidden" name="notification_settings" value="1">
                            <div class="form-check form-switch mb-3">
                                <input 
                                    class="form-check-input" 
                                    type="checkbox" 
                                    name="email_notifications" 
                                    id="emailNotifications"
                                    <?= $settings['email_notifications'] ? 'checked' : '' ?>
                                >
                                <label class="form-check-label" for="emailNotifications">
                                    إشعارات البريد الإلكتروني
                                </label>
                            </div>
                            <div class="form-check form-switch mb-3">
                                <input 
                                    class="form-check-input" 
                                    type="checkbox" 
                                    name="sms_notifications" 
                                    id="smsNotifications"
                                    <?= $settings['sms_notifications'] ? 'checked' : '' ?>
                                >
                                <label class="form-check-label" for="smsNotifications">
                                    إشعارات الرسائل النصية
                                </label>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save me-2"></i>حفظ إعدادات الإشعارات
                            </button>
                        </form>
                    </div>

                  <!-- إعدادات الخصوصية -->
                  <div class="settings-section">
                        <h5>
                            <i class="bi bi-shield-lock me-2 text-success"></i>
                            إعدادات الخصوصية
                        </h5>
                        <form method="POST">
                            <input type="hidden" name="privacy_settings" value="1">
                            <div class="mb-3">
                                <label                                class="form-label">
                                    رؤية الملف الشخصي
                                </label>
                                <select 
                                    name="profile_visibility" 
                                    class="form-select" 
                                    id="profileVisibility"
                                >
                                    <option 
                                        value="public" 
                                        <?= $settings['profile_visibility'] == 'public' ? 'selected' : '' ?>
                                    >
                                        عام (الكل يمكنه الرؤية)
                                    </option>
                                    <option 
                                        value="private" 
                                        <?= $settings['profile_visibility'] == 'private' ? 'selected' : '' ?>
                                    >
                                        خاص (فقط أنا)
                                    </option>
                                    <option 
                                        value="friends" 
                                        <?= $settings['profile_visibility'] == 'friends' ? 'selected' : '' ?>
                                    >
                                        الأصدقاء فقط
                                    </option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save me-2"></i>حفظ إعدادات الخصوصية
                            </button>
                        </form>
                    </div>

                    <!-- إعدادات المظهر -->
                    <div class="settings-section">
                        <h5>
                            <i class="bi bi-palette me-2 text-info"></i>
                            إعدادات المظهر
                        </h5>
                        <form method="POST">
                            <input type="hidden" name="theme_settings" value="1">
                            <div class="mb-3">
                                <label class="form-label">اختيار المظهر</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-check-inline">
                                            <input 
                                                class="form-check-input" 
                                                type="radio" 
                                                name="theme" 
                                                id="lightTheme" 
                                                value="light"
                                                <?= $settings['theme_preference'] == 'light' ? 'checked' : '' ?>
                                            >
                                            <label class="form-check-label" for="lightTheme">
                                                <i class="bi bi-sun me-2"></i>فاتح
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-check-inline">
                                            <input 
                                                class="form-check-input" 
                                                type="radio" 
                                                name="theme" 
                                                id="darkTheme" 
                                                value="dark"
                                                <?= $settings['theme_preference'] == 'dark' ? 'checked' : '' ?>
                                            >
                                            <label class="form-check-label" for="darkTheme">
                                                <i class="bi bi-moon me-2"></i>داكن
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save me-2"></i>حفظ إعدادات المظهر
                            </button>
                        </form>
                    </div>

                    <!-- إعدادات متقدمة -->
                    <div class="settings-section">
                        <h5>
                            <i class="bi bi-shield-exclamation me-2 text-danger"></i>
                            إعدادات متقدمة
                        </h5>
                        <div class="d-grid gap-2">
                            <a href="change_password.php" class="btn btn-outline-danger">
                                <i class="bi bi-lock me-2"></i>تغيير كلمة المرور
                            </a>
                            <a href="delete_account.php" class="btn btn-outline-dark">
                                <i class="bi bi-trash me-2"></i>حذف الحساب
                            </a>
                        </div>
                    </div>

                    <!-- إعدادات خصوصية المفضلة -->
                    <div class="container mt-4">
        <?php if(isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success">
                <?= $_SESSION['success_message'] ?>
                <?php unset($_SESSION['success_message']); ?>
            </div>
        <?php endif; ?>

        <?php if(isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger">
                <?= $_SESSION['error_message'] ?>
                <?php unset($_SESSION['error_message']); ?>
            </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-body">
                <h5 class="card-title">إعدادات الخصوصية</h5>
                <form method="POST" action="settings.php">
                    <div class="mb-3">
                        <label class="form-label">خصوصية المفضلة:</label>
                        <select name="favorites_privacy" class="form-select">
                            <option value="0" <?= ($user['favorites_privacy'] ?? 0) == 0 ? 'selected' : '' ?>>خاص</option>
                            <option value="1" <?= ($user['favorites_privacy'] ?? 0) == 1 ? 'selected' : '' ?>>عام</option>
                        </select>
                        <div class="form-text">اختر ما إذا كنت تريد أن تكون مفضلتك مرئية للزوار</div>
                    </div>
                    <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
                </form>
            </div>
        </div>
    </div>

                </div>
            </div>
        </div>
    </div>

    <div class="card mb-3">
    <div class="card-body">
     
    <!-- Bootstrap 5.3 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة تأثيرات تفاعلية
        const settingsSections = document.querySelectorAll('.settings-section');
        settingsSections.forEach(section => {
            section.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.transition = 'transform 0.3s ease';
            });
            
            section.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

               // معالجة الرسائل
               const alertElements = document.querySelectorAll('.alert');
        alertElements.forEach(alert => {
            setTimeout(() => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });

        // التحقق من صحة الإعدادات عند التغيير
        const notificationForm = document.querySelector('form[name="notification_settings"]');
        if (notificationForm) {
            notificationForm.addEventListener('change', function() {
                const emailCheckbox = document.getElementById('emailNotifications');
                const smsCheckbox = document.getElementById('smsNotifications');

                // التأكد من وجود اختيار واحد على الأقل
                if (!emailCheckbox.checked && !smsCheckbox.checked) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'تحذير',
                        text: 'يجب اختيار وسيلة إشعار واحدة على الأقل',
                        confirmButtonText: 'حسناً'
                    });
                }
            });
        }
    });

    // دالة مساعدة لعرض رسائل التأكيد
    function showConfirmation(message) {
        Swal.fire({
            icon: 'success',
            title: 'تم الحفظ',
            text: message,
            timer: 2000,
            showConfirmButton: false
        });
    }
    // دالة للتحقق من صحة الإدخال
function validateInput($input, $type = 'string') {
    switch($type) {
        case 'boolean':
            return filter_var($input, FILTER_VALIDATE_BOOLEAN);
        case 'email':
            return filter_var($input, FILTER_VALIDATE_EMAIL);
        case 'enum':
            $validValues = func_get_args()[2] ?? [];
            return in_array($input, $validValues) ? $input : null;
        default:
            return htmlspecialchars(trim($input));
    }
}

// دالة للسجل
function logAction($action, $details = []) {
    global $db;
    try {
        $stmt = $db->prepare("INSERT INTO user_logs 
            (user_id, action, details, created_at) 
            VALUES (?, ?, ?, NOW())");
        $stmt->execute([
            $_SESSION['user_id'], 
            $action, 
            json_encode($details)
        ]);
    } catch(PDOException $e) {
        error_log("خطأ في تسجيل الإجراء: " . $e->getMessage());
    }
}
    </script>

    <!-- Sweet Alert 2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  
</body>
</html>
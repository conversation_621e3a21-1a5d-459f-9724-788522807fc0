<?php
require_once '../config.php';

if (isset($_GET['id'])) {
    try {
        $stmt = $db->prepare("SELECT * FROM cities WHERE id = ?");
        $stmt->execute([(int)$_GET['id']]);
        $city = $stmt->fetch(PDO::FETCH_ASSOC);
        
        header('Content-Type: application/json');
        echo json_encode($city);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
    }
}
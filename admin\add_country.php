<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once '../config.php';
require_once 'auth_check.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $countryName = trim($_POST['country_name']);
        
        if (empty($countryName)) {
            throw new Exception('اسم الدولة مطلوب');
        }

        // Check if country exists
        $checkStmt = $db->prepare("SELECT COUNT(*) FROM countries WHERE name = ?");
        $checkStmt->execute([$countryName]);
        if ($checkStmt->fetchColumn() > 0) {
            throw new Exception('هذه الدولة موجودة مسبقاً');
        }

        // Insert new country
        $stmt = $db->prepare("INSERT INTO countries (name) VALUES (?)");
        $stmt->execute([$countryName]);
        
        $_SESSION['success'] = 'تمت إضافة الدولة بنجاح';
        header('Location: countries.php');
        exit;

    } catch (Exception $e) {
        $_SESSION['error'] = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة دولة جديدة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/css/admin.css" rel="stylesheet">
</head>
<body>
    <div class="d-flex">
        <?php include 'sidebar.php'; ?>
        
        <div class="main-content p-4 w-100">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>إضافة دولة جديدة</h2>
                <a href="countries.php" class="btn btn-secondary">
                    <i class="bi bi-arrow-right me-1"></i>
                    عودة
                </a>
            </div>

            <?php if(isset($_SESSION['error'])): ?>
                <div class="alert alert-danger"><?= $_SESSION['error']; unset($_SESSION['error']); ?></div>
            <?php endif; ?>

            <div class="card">
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">اسم الدولة</label>
                            <input type="text" class="form-control" name="country_name" required>
                        </div>
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-plus-lg me-1"></i>
                                إضافة الدولة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
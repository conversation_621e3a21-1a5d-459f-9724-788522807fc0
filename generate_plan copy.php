<?php
// generate_plan.php
session_start();

function callGoogleAI($prompt) {
    $apiKey = 'AIzaSyA7G2VbfTCoh392tSwmD8lNOPRwqJFBuCw';
    $url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
    
    $data = [
        'contents' => [
            ['parts' => [['text' => $prompt]]]
        ]
    ];

    $ch = curl_init($url . '?key=' . $apiKey);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);

    $response = curl_exec($ch);
    curl_close($ch);

    return json_decode($response, true);
}

function extractItineraryDetails($itinerary) {
    $details = [];
    $days = explode("\n\n", $itinerary);
    
    foreach ($days as $day) {
        if (!empty(trim($day))) {
            $lines = explode("\n", $day);
            $dayTitle = array_shift($lines);
            
            $dayDetails = [
                'title' => $dayTitle,
                'activities' => []
            ];
            
            foreach ($lines as $line) {
                $line = trim($line);
                if (!empty($line)) {
                    // استخراج المعلومات الأساسية
                    preg_match('/^(.*?):(.*)$/', $line, $matches);
                    
                    if (count($matches) == 3) {
                        $time = trim($matches[1]);
                        $activity = trim($matches[2]);
                        
                        // محاولة استخراج الرابط إن وجد
                        preg_match('/https?:\/\/\S+/', $activity, $linkMatches);
                        $link = !empty($linkMatches) ? $linkMatches[0] : '';
                        
                        // إزالة الرابط من نص النشاط
                        $activityText = trim(preg_replace('/https?:\/\/\S+/', '', $activity));
                        
                        $dayDetails['activities'][] = [
                            'time' => $time,
                            'activity' => $activityText,
                            'link' => $link
                        ];
                    }
                }
            }
            
            $details[] = $dayDetails;
        }
    }
    
    return $details;
}

// Combine all preferences into a prompt
$tourData = $_SESSION['tour_data'];
$preferences = $_POST;

$prompt = "اقترح برنامج سياحي مفصل لمدينة {$tourData['city']} لمدة {$preferences['days']} أيام ";
$prompt .= "للإقامة في " . implode(" أو ", $preferences['accommodation']) . " ";
$prompt .= "ويشمل " . implode(" و ", $preferences['restaurants']) . " ";
$prompt .= "مع أنشطة " . implode(" و ", $preferences['activities']) . ". ";
$prompt .= "قم بتقسيم البرنامج على الأيام مع تحديد الأوقات والأماكن بشكل دقيق. ";
$prompt .= "تأكد من وضع روابط Google Maps دقيقة لكل نشاط. ";
$prompt .= "اذكر التكلفة التقديرية لكل نشاط.";

$response = callGoogleAI($prompt);
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>البرنامج السياحي المقترح</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .itinerary-day { margin-bottom: 20px; }
        .day-header { background: #f8f9fa; padding: 10px; margin-bottom: 15px; }
        .table-activities { width: 100%; }
        .table-activities th, .table-activities td { padding: 10px; text-align: right; }
        .activity-link { color: blue; text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container my-5">
        <h2 class="text-center mb-4">البرنامج السياحي المقترح</h2>
        
        <?php
        if (isset($response['candidates'][0]['content']['parts'][0]['text'])) {
            $itinerary = $response['candidates'][0]['content']['parts'][0]['text'];
            $details = extractItineraryDetails($itinerary);
            
            foreach ($details as $day) {
                echo '<div class="itinerary-day">';
                echo '<div class="day-header">' . htmlspecialchars($day['title']) . '</div>';
                
                echo '<table class="table table-striped table-activities">';
                echo '<thead><tr><th>الوقت</th><th>النشاط</th><th>رابط الموقع</th></tr></thead>';
                echo '<tbody>';
                
                foreach ($day['activities'] as $activity) {
                    echo '<tr>';
                    echo '<td>' . htmlspecialchars($activity['time']) . '</td>';
                    echo '<td>' . htmlspecialchars($activity['activity']) . '</td>';
                    echo '<td>' . 
                        (!empty($activity['link']) 
                            ? '<a href="' . htmlspecialchars($activity['link']) . '" target="_blank" class="activity-link">فتح الموقع</a>' 
                            : 'لا يوجد رابط') . 
                         '</td>';
                    echo '</tr>';
                }
                
                echo '</tbody></table>';
                echo '</div>';
            }
        } else {
            echo '<div class="alert alert-warning">عذراً، حدث خطأ في إنشاء البرنامج. يرجى المحاولة مرة أخرى.</div>';
        }
        ?>
        
        <div class="text-center mt-4">
            <a href="index.php" class="btn btn-primary">إنشاء برنامج جديد</a>
            <button onclick="window.print()" class="btn btn-secondary">طباعة البرنامج</button>
        </div>
    </div>
</body>
</html>
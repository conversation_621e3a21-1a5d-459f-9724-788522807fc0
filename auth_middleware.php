<?php
require_once 'session_handler.php';

// يمكنك إضافة وظائف إضافية للتحقق من الصلاحيات إذا لزم الأمر
function checkUserTourAccess($tourId, $db) {
    $userId = getCurrentUserId();
    
    $stmt = $db->prepare("SELECT * FROM tour_plans WHERE id = :id AND user_id = :user_id");
    $stmt->bindValue(':id', $tourId);
    $stmt->bindValue(':user_id', $userId);
    $stmt->execute();
    
    return $stmt->fetch() !== false;
}
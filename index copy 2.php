<?php
// index.php
session_start();
require_once 'config.php';
require_once 'session_handler.php';
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>برنامج السياحة العالمية</title>
     <!-- Bootstrap 5.3 CSS RTL -->
     <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f4f6f9;
        }
        .profile-container {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            padding: 30px;
            transition: all 0.3s ease;
        }
        .profile-container:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        .profile-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .profile-avatar {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            border: 5px solid #3498db;
        }
        .stats-card {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .stats-card:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .btn-edit-profile {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        .btn-ai {
            background-color: #6f42c1;
            color: white;
            margin-right: 10px;
        }
        .btn-ai:hover {
            background-color: #5a32a3;
            color: white;
        }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .spinner-container {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        .spinner-border {
            width: 3rem;
            height: 3rem;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<!-- Add these styles to your existing CSS -->
<style>
.tourism-info-card {
    margin-bottom: 30px;
}

.tourism-info-card .card {
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.tourism-info-card .card:hover {
    transform: translateY(-5px);
}

.tourism-info-card .card-title {
    color: #2c3e50;
    font-weight: bold;
}

.tourism-info-card i {
    margin-right: 8px;
}

.tourism-info-card ul li {
    padding: 5px 0;
}

.tourism-info-card h5 {
    color: #34495e;
    margin-top: 15px;
}
</style>

<style>
    .navbar {
        margin-bottom: 20px;
        background-color: #f8f9fa !important;
    }
    .nav-link {
        transition: all 0.3s ease;
    }
    .nav-link:hover {
        color: #3498db !important;
        transform: translateY(-2px);
    }
    .dropdown-menu {
        min-width: 200px;
        left: auto !important;
        right: 0 !important;
        margin-top: 10px;
        transform: translateX(-10px);
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        border: none;
        border-radius: 10px;
    }
</style>
</head>
<?php include 'navbar.php'; ?>
<body>
    <div class="container form-container">
        <h2 class="text-center mb-4">برنامج السياحة العالمية</h2>
        
        <form id="tourForm" onsubmit="return checkLogin(event)" action="preferences.php" method="POST">
            <div class="mb-3">
                <label class="form-label">اختر الدولة:</label>
                <select class="form-select" name="country" id="countrySelect" required>
                    <option value="">اختر الدولة</option>
                    <?php
                    $result = mysqli_query($conn, "SELECT * FROM countries ORDER BY name");
                    while($row = mysqli_fetch_assoc($result)) {
                        echo "<option value='".$row['id']."'>".$row['name']."</option>";
                    }
                    ?>
                </select>
            </div>

            <div class="mb-3">
                <label class="form-label">اختر المدينة:</label>
                <select class="form-select" name="city" id="citySelect" required>
                    <option value="">اختر المدينة أولاً</option>
                </select>
            </div>

            <div class="mb-3">
                <label class="form-label">عدد المسافرين:</label>
                <select class="form-select" name="travelers" required>
                    <?php for($i=1; $i<=10; $i++) { ?>
                        <option value="<?php echo $i; ?>"><?php echo $i; ?> مسافر</option>
                    <?php } ?>
                </select>
            </div>

            <div class="text-center mb-4">
            <button type="submit" class="btn btn-primary btn-lg">عمل برنامج سياحي</button>
                <button type="button" class="btn btn-ai btn-lg" onclick="getAISuggestion()">
                    <i class="bi bi-robot"></i> جربنا..
                </button>
            </div>
        </form>
    </div>
<!-- Add after the form section -->
<div class="container mt-5">
    <div class="tourism-info-card">
        <?php
        // Get random tourism info
        $stmt = $db->query("SELECT * FROM tourism_info ORDER BY RAND() LIMIT 1");
        $info = $stmt->fetch(PDO::FETCH_ASSOC);
        if($info):
        ?>
        <div class="card">
            <div class="card-body">
                <h3 class="card-title mb-4 text-center">
                    <i class="bi bi-geo-alt-fill text-primary"></i>
                    اكتشف <?= htmlspecialchars($info['city_name']) ?> - <?= htmlspecialchars($info['country_name']) ?>
                </h3>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="bi bi-people-fill text-info"></i>
                                <strong>عدد السكان:</strong> <?= number_format($info['population']) ?>
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-cash-coin text-success"></i>
                                <strong>العملة:</strong> <?= htmlspecialchars($info['currency']) ?>
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-translate text-primary"></i>
                                <strong>اللغة:</strong> <?= htmlspecialchars($info['language']) ?>
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-thermometer-half text-danger"></i>
                                <strong>درجة الحرارة:</strong> <?= $info['current_temp'] ?>°C
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="bi bi-star-fill text-warning"></i> أشهر المعالم</h5>
                        <p><?= nl2br(htmlspecialchars($info['famous_places'])) ?></p>
                        
                        <h5><i class="bi bi-calendar-check text-success"></i> أفضل وقت للزيارة</h5>
                        <p><?= htmlspecialchars($info['best_time_to_visit']) ?></p>
                    </div>
                </div>
                <div class="mt-3">
                    <h5><i class="bi bi-pin-map-fill text-primary"></i> المدن الرئيسية</h5>
                    <p><?= htmlspecialchars($info['main_cities']) ?></p>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $('#countrySelect').change(function() {
            var countryId = $(this).val();
            if(countryId) {
                $.ajax({
                    url: 'get_cities.php',
                    type: 'POST',
                    data: {country_id: countryId},
                    success: function(response) {
                        $('#citySelect').html(response);
                    },
                    error: function() {
                        alert('حدث خطأ في تحميل المدن');
                    }
                });
            } else {
                $('#citySelect').html('<option value="">اختر المدينة أولاً</option>');
            }
        });

        async function getAISuggestion() {
            const citySelect = document.querySelector('select[name="city"]');
            if (!citySelect || !citySelect.value) {
                alert('الرجاء اختيار المدينة أولاً');
                return;
            }

            // Show loading spinner
            document.getElementById('loadingSpinner').style.display = 'flex';

            try {
                const response = await fetch('get_ai_suggestion.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ city: citySelect.value })
                });

                const result = await response.json();
                if (result.suggestion && result.suggestion.candidates && result.suggestion.candidates[0].content) {
                    const text = result.suggestion.candidates[0].content.parts[0].text;
                    document.getElementById('aiSuggestionContent').innerText = text;
                    new bootstrap.Modal(document.getElementById('aiSuggestionModal')).show();
                } else {
                    alert('عذراً، لم نتمكن من الحصول على اقتراح في الوقت الحالي');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('حدث خطأ في جلب الاقتراح');
            } finally {
                // Hide loading spinner
                document.getElementById('loadingSpinner').style.display = 'none';
            }
        }

        function checkLogin(event) {
            <?php if (!isset($_SESSION['user_id'])): ?>
            event.preventDefault();
            Swal.fire({
                title: 'تنبيه!',
                text: 'الرجاء تسجيل الدخول لتفعيل الخدمة، أو قم بالتسجيل إذا لم تكن مسجلاً',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'تسجيل الدخول',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = 'login.php';
                }
            });
            return false;
            <?php else: ?>
            return true;
            <?php endif; ?>
        }
    </script>
    <!-- Add Modal for showing AI suggestion -->
    <div class="modal fade" id="aiSuggestionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">اقتراح برنامج سياحي</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" style="white-space: pre-line;">
                    <div id="aiSuggestionContent"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="saveFavorite()">حفظ في المفضلة</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add spinner HTML before modal -->
    <div class="loading-overlay" id="loadingSpinner" style="display: none;">
        <div class="spinner-container">
            <div class="spinner-border text-primary" role="status"></div>
            <div class="mt-2">جاري إعداد البرنامج...</div>
        </div>
    </div>

    <script>
    async function saveFavorite() {
        <?php if (!isset($_SESSION['user_id'])): ?>
            Swal.fire({
                title: 'تنبيه!',
                text: 'أخي الزائر.. لكي تتمكن من الحفظ قم بالتسجيل أولاً',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'تسجيل الدخول',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = 'login.php';
                }
            });
            return;
        <?php else: ?>
            const suggestionText = document.getElementById('aiSuggestionContent').innerText;
            const citySelect = document.querySelector('select[name="city"]');
            
            try {
                const response = await fetch('save_favorite.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        suggestion: suggestionText,
                        city: citySelect.options[citySelect.selectedIndex].text
                    })
                });
                
                const result = await response.json();
                if (result.success) {
                    Swal.fire({
                        title: 'تم!',
                        text: 'تم الحفظ في المفضلة بنجاح',
                        icon: 'success'
                    });
                }
            } catch (error) {
                console.error('Error:', error);
                Swal.fire({
                    title: 'خطأ!',
                    text: 'حدث خطأ أثناء الحفظ',
                    icon: 'error'
                });
            }
        <?php endif; ?>
    }
    </script>

      <!-- Bootstrap JS (اختياري) -->
      <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- رابط أيقونات Bootstrap (اختياري) -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
</body>
</html>
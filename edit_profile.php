<?php
session_start();
require_once 'config.php';
require_once 'auth_middleware.php';
requireLogin();

$userId = $_SESSION['user_id'];
$success_message = '';
$error_message = '';

// Get user data
try {
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Debug log
    error_log("User data fetched: " . print_r($user, true));
    
} catch(PDOException $e) {
    error_log("Error fetching user: " . $e->getMessage());
    $error_message = 'حدث خطأ في جلب البيانات';
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $stmt = $db->prepare("
            UPDATE users 
            SET full_name = ?,
                email = ?,
                phone = ?,
                bio = ?,
                city = ?,
                country = ?,
                interests = ?
            WHERE id = ?
        ");
        
        $stmt->execute([
            trim($_POST['full_name']),
            trim($_POST['email']),
            trim($_POST['phone']),
            trim($_POST['bio']),
            trim($_POST['city']),
            trim($_POST['country']), 
            trim($_POST['interests']),
            $userId
        ]);

        $success_message = 'تم تحديث البيانات بنجاح';
        
    } catch(PDOException $e) {
        error_log("Update Error: " . $e->getMessage());
        $error_message = 'حدث خطأ أثناء تحديث البيانات';
    }
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل الملف الشخصي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</head>
<body>
    <?php include 'navbar.php'; ?>

    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body">
                        <h3 class="card-title mb-4">
                            <i class="bi bi-person-gear"></i>
                            تعديل الملف الشخصي
                        </h3>

                        <?php if($success_message): ?>
                            <div class="alert alert-success"><?= $success_message ?></div>
                        <?php endif; ?>

                        <?php if($error_message): ?>
                            <div class="alert alert-danger"><?= $error_message ?></div>
                        <?php endif; ?>

                        <form method="POST" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" name="full_name" 
                                       value="<?= htmlspecialchars($user['full_name'] ?? '') ?>" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" name="email" 
                                       value="<?= htmlspecialchars($user['email'] ?? '') ?>" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" name="phone" 
                                       value="<?= htmlspecialchars($user['phone'] ?? '') ?>">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">نبذة شخصية</label>
                                <textarea class="form-control" name="bio" rows="4"><?= htmlspecialchars($user['bio'] ?? '') ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">المدينة</label>
                                <input type="text" class="form-control" name="city" 
                                       value="<?= htmlspecialchars($user['city'] ?? '') ?>">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">البلد</label>
                                <input type="text" class="form-control" name="country" 
                                       value="<?= htmlspecialchars($user['country'] ?? '') ?>">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الاهتمامات</label>
                                <textarea class="form-control" name="interests" rows="4"><?= htmlspecialchars($user['interests'] ?? '') ?></textarea>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-save me-2"></i>حفظ التغييرات
                                </button>
                                <a href="profile.php" class="btn btn-secondary">
                                    <i class="bi bi-x-circle me-2"></i>إلغاء
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
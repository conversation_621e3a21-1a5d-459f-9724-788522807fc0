<?php
// تضمين ملفات الإعداد والجلسة
session_start();
require_once 'config.php';
require_once 'session_handler.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من وجود معرف الرحلة
$tourId = $_GET['id'] ?? null;
if (!$tourId) {
    die('لم يتم تحديد معرف الرحلة');
}

// استرجاع بيانات الرحلة من قاعدة البيانات
try {
    $stmt = $db->prepare("SELECT * FROM tour_plans WHERE id = ? AND user_id = ?");
    $stmt->execute([$tourId, $_SESSION['user_id']]);
    $tour = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$tour) {
        die('لم يتم العثور على الرحلة المطلوبة');
    }
} catch (PDOException $e) {
    die('خطأ في قاعدة البيانات: ' . $e->getMessage());
}

function formatTourContent($content) {
    // تحويل النص إلى مصفوفة من الأسطر
    $lines = explode("\n", $content);
    $formattedContent = '';
    $inList = false;
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        // إزالة علامات النجوم في بداية ونهاية النص
        $line = preg_replace('/^\*\*(.*)\*\*$/', '$1', $line);
        
        // معالجة العناوين الرئيسية
        if (preg_match('/^#+ (.*)$/', $line, $matches)) {
            if ($inList) {
                $formattedContent .= '</ul>';
                $inList = false;
            }
            $formattedContent .= sprintf('<h2 class="tour-day-title">%s</h2>', $matches[1]);
            continue;
        }
        
        // معالجة عناوين الأيام
        if (preg_match('/^اليوم (ال[أإا]ول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن|التاسع|العاشر|\d+)/', $line)) {
            if ($inList) {
                $formattedContent .= '</ul>';
                $inList = false;
            }
            $formattedContent .= sprintf('<h3 class="day-header">%s</h3><ul class="timeline">', $line);
            $inList = true;
            continue;
        }
        
        // معالجة الروابط والنصوص العادية
        if (strpos($line, '<a href') !== false) {
            // استخراج الوقت والنشاط من النص
            if (preg_match('/([\d:]+\s+(?:صباحًا|مساءً|ظهرًا)):\s+(.*)/', $line, $matches)) {
                $time = $matches[1];
                $activity = $matches[2];
                
                // معالجة الرابط
                $activity = preg_replace_callback('/<a href="([^"]+)"[^>]*><i[^>]*><\/i><\/a>/', 
                    function($m) {
                        return sprintf('<a href="%s" class="location-link" target="_blank"><i class="fa-solid fa-location-dot"></i> عرض الموقع</a>', $m[1]);
                    }, 
                    $activity
                );
                
                if (!$inList) {
                    $formattedContent .= '<ul class="timeline">';
                    $inList = true;
                }
                
                $formattedContent .= sprintf(
                    '<li class="timeline-item">
                        <div class="timeline-time">%s</div>
                        <div class="timeline-content">%s</div>
                    </li>',
                    $time,
                    $activity
                );
            }
        } else {
            // النصوص العادية
            if (!$inList) {
                $formattedContent .= sprintf('<p class="tour-text">%s</p>', $line);
            } else {
                $formattedContent .= sprintf('<li class="timeline-item"><div class="timeline-content">%s</div></li>', $line);
            }
        }
    }
    
    if ($inList) {
        $formattedContent .= '</ul>';
    }
    
    return $formattedContent;
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الرحلة - <?= htmlspecialchars($tour['city']) ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .tour-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            direction: rtl;
            font-family: 'Tajawal', sans-serif;
        }
        
        .tour-day-title {
            background-color: #f8f9fa;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            color:rgb(80, 44, 44);
            font-size: 1.5em;
        }
        
        .day-header {
            color:rgb(219, 52, 52);
            margin: 25px 0 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
            font-size: 1.8em;
        }
        
        .timeline {
            list-style: none;
            padding: 0;
            position: relative;
        }
        
        .timeline:before {
            content: '';
            position: absolute;
            top: 0;
            right: 20px;
            height: 100%;
            width: 2px;
            background: #e9ecef;
        }
        
        .timeline-item {
            margin-bottom: 20px;
            position: relative;
            padding-right: 50px;
        }
        
        .timeline-item:before {
            content: '';
            position: absolute;
            right: 16px;
            top: 0;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background:rgb(41, 82, 214);
            border: 2px solid #fff;
        }
        
        .timeline-time {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .timeline-content {
            background: #fff;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .location-link {
            display: inline-block;
            margin-right: 10px;
            color: #3498db;
            text-decoration: none;
            transition: color 0.3s;
            padding: 5px 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        
        .location-link:hover {
            color: #2980b9;
            background-color: #e9ecef;
        }
        
        .tour-text {
            line-height: 1.6;
            margin: 15px 0;
        }

        .tour-header {
            background-color: #f8f9fa;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .tour-title {
            margin: 0;
            color: #2c3e50;
            font-size: 2em;
            margin-bottom: 10px;
        }

        .tour-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .meta-item {
            padding: 10px;
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .meta-label {
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="tour-container">
        <div class="tour-header">
            <h1 class="tour-title">رحلة إلى <?= htmlspecialchars($tour['city']) ?></h1>
            <div class="tour-meta">
                <div class="meta-item">
                    <div class="meta-label">المدة</div>
                    <?= $tour['days_count'] ?> أيام
                </div>
                <div class="meta-item">
                    <div class="meta-label">عدد المسافرين</div>
                    <?= $tour['travelers_count'] ?> أشخاص
                </div>
                <div class="meta-item">
                    <div class="meta-label">الميزانية التقديرية</div>
                    <?= number_format($tour['total_budget']) ?> ريال
                </div>
                <div class="meta-item">
                    <div class="meta-label">تاريخ الإنشاء</div>
                    <?= date('Y/m/d', strtotime($tour['created_at'])) ?>
                </div>
            </div>
        </div>

        <?php echo formatTourContent($tour['plan_details']); ?>
    </div>
</body>
</html>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>اختبار جلب المدن</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>اختبار جلب المدن</h1>
    
    <div class="test-section">
        <h3>اختبار مباشر لملف get_cities.php</h3>
        
        <?php
        // اختبار الدول المتاحة
        $servername = "localhost";
        $username = "root";
        $password = "";
        $dbname = "tourism_db";

        $conn = new mysqli($servername, $username, $password, $dbname);

        if ($conn->connect_error) {
            echo "<p style='color: red;'>خطأ في الاتصال: " . $conn->connect_error . "</p>";
        } else {
            echo "<p style='color: green;'>✓ الاتصال بقاعدة البيانات نجح</p>";
            
            $conn->set_charset("utf8mb4");
            
            // عرض الدول المتاحة
            echo "<h4>الدول المتاحة:</h4>";
            $result = $conn->query("SELECT id, name FROM countries ORDER BY name");
            if ($result && $result->num_rows > 0) {
                echo "<ul>";
                while($row = $result->fetch_assoc()) {
                    echo "<li>ID: " . $row['id'] . " - " . $row['name'] . "</li>";
                }
                echo "</ul>";
                
                // اختبار جلب المدن لكل دولة
                echo "<h4>اختبار جلب المدن:</h4>";
                $countries = $conn->query("SELECT id, name FROM countries ORDER BY name");
                while($country = $countries->fetch_assoc()) {
                    echo "<div class='result'>";
                    echo "<strong>" . $country['name'] . " (ID: " . $country['id'] . "):</strong><br>";
                    
                    // محاكاة POST request
                    $_POST['country_id'] = $country['id'];
                    ob_start();
                    include 'get_cities.php';
                    $cities_html = ob_get_clean();
                    
                    echo "<select style='width: 100%; margin: 5px 0;'>" . $cities_html . "</select>";
                    echo "</div>";
                }
            } else {
                echo "<p style='color: red;'>لا توجد دول في قاعدة البيانات</p>";
            }
            
            $conn->close();
        }
        ?>
    </div>
    
    <div class="test-section">
        <h3>اختبار AJAX</h3>
        <p>اختر دولة لاختبار AJAX:</p>
        
        <select id="testCountry" style="width: 200px; margin: 10px;">
            <option value="">اختر الدولة</option>
            <?php
            $conn = new mysqli($servername, $username, $password, $dbname);
            if (!$conn->connect_error) {
                $conn->set_charset("utf8mb4");
                $result = $conn->query("SELECT id, name FROM countries ORDER BY name");
                while($row = $result->fetch_assoc()) {
                    echo "<option value='".$row['id']."'>".$row['name']."</option>";
                }
                $conn->close();
            }
            ?>
        </select>
        
        <select id="testCity" style="width: 200px; margin: 10px;">
            <option value="">اختر المدينة أولاً</option>
        </select>
        
        <div id="ajaxResult" class="result" style="display: none;"></div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $('#testCountry').change(function() {
            var countryId = $(this).val();
            var resultDiv = $('#ajaxResult');
            
            if(countryId) {
                resultDiv.show().html('جاري التحميل...');
                
                $.ajax({
                    url: 'get_cities.php',
                    type: 'POST',
                    data: {country_id: countryId},
                    success: function(response) {
                        $('#testCity').html(response);
                        resultDiv.html('<span style="color: green;">✓ تم تحميل المدن بنجاح</span>');
                    },
                    error: function(xhr, status, error) {
                        $('#testCity').html('<option value="">خطأ في التحميل</option>');
                        resultDiv.html('<span style="color: red;">✗ خطأ في AJAX: ' + error + '</span>');
                    }
                });
            } else {
                $('#testCity').html('<option value="">اختر المدينة أولاً</option>');
                resultDiv.hide();
            }
        });
    </script>
    
    <p><a href="index.php">العودة للصفحة الرئيسية</a></p>
</body>
</html>

<?php
require_once '../config.php';

if (isset($_GET['id'])) {
    try {
        $stmt = $db->prepare("SELECT id, name FROM countries WHERE id = ?");
        $stmt->execute([(int)$_GET['id']]);
        $country = $stmt->fetch(PDO::FETCH_ASSOC);
        
        header('Content-Type: application/json');
        echo json_encode($country);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
    }
}
<?php
header('Content-Type: text/html; charset=utf-8');
mb_internal_encoding('UTF-8');
require_once 'config.php';
require_once 'session_handler.php';
requireLogin();

// التحقق من معرف الرحلة
$tourId = $_GET['id'] ?? 0;

// جلب تفاصيل الرحلة
$stmt = $db->prepare("SELECT * FROM tour_plans WHERE id = ? AND user_id = ?");
$stmt->execute([$tourId, $_SESSION['user_id']]);
$tour = $stmt->fetch();

if (!$tour) {
    // توجيه في حال عدم وجود الرحلة
    header("Location: my_tours.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الرحلة</title>
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css">
    <style>
        @media print {
            .no-print {
                display: none;
            }
        }
    </style>
    <style>
        /* body {
            font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
            line-height: 1.8;
        } */
        .tour-details {
            
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        .day-header {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .activity-item {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
    </style>
    <style>
.arabic-content {
    direction: rtl;
    text-align: right;
    font-family: 'Tajawal', sans-serif;
    line-height: 1.8;
    padding: 20px;
}
.plan-text {
    white-space: pre-line;
    word-wrap: break-word;
}
.plan-text p {
    margin-bottom: 10px;
}
</style>
<style>
.arabic-content {
    direction: rtl;
    text-align: right;
    font-family: 'Tajawal', sans-serif;
    line-height: 1.8;
    padding: 20px;
}
.plan-text {
    white-space: pre-line;
    word-wrap: break-word;
}
.plan-text p {
    margin-bottom: 10px;
}
</style>

<!-- CSS إضافي -->
<style>
    .navbar {
        margin-bottom: 20px;
        background-color: #f8f9fa !important;
    }
    .nav-link {
        transition: all 0.3s ease;
    }
    .nav-link:hover {
        color: #3498db !important;
        transform: translateY(-2px);
    }
    .dropdown-menu {
        min-width: 200px;
    }
</style>
<style>
.arabic-content {
    direction: rtl;
    text-align: right;
    font-family: 'Tajawal', sans-serif;
    line-height: 1.8;
    padding: 20px;
}
.plan-text {
    white-space: pre-line;
    word-wrap: break-word;
}
.plan-text p {
    margin-bottom: 10px;
}
</style>
<style>
.arabic-content {
    direction: rtl;
    text-align: right;
    font-family: 'Tajawal', sans-serif;
    line-height: 2;
    font-size: 16px;
    padding: 20px;
}
.day-title {
    color: #2c3e50;
    font-size: 1.4em;
    font-weight: bold;
    margin-top: 20px;
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 5px;
}
.note-text {
    color: #e74c3c;
    font-size: 1.1em;
    font-weight: 500;
    margin: 15px 0;
    padding: 10px;
    background-color:rgb(190, 24, 24);
    border-radius: 5px;
}
.regular-text {
    font-size: 1.1em;
    margin-bottom: 10px;
    padding: 5px 15px;
}
.plan-text {
    white-space: pre-line;
    word-wrap: break-word;
}
</style>
<style>
.highlight-header {
    display: block;
    font-size: 1.3em;
    font-weight: bold;
    color: #2c3e50;
    margin-top: 20px;
    margin-bottom: 10px;
    background-color: #f8f9fa;
    padding: 8px;
    border-radius: 5px;
}
</style>
</head>
<?php include 'navbar.php'; ?>
<body>
    <div class="container my-5">
        <div class="card">
            <div class="card-header">
                <h3>تفاصيل رحلة <?= htmlspecialchars($tour['city']) ?></h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>معلومات الرحلة الأساسية</h5>
                        <ul class="list-unstyled">
                            <li><strong>المدينة:</strong> <?= htmlspecialchars($tour['city']) ?></li>
                            <li><strong>المدة:</strong> <?= $tour['days_count'] ?> أيام</li>
                            <li><strong>عدد المسافرين:</strong> <?= $tour['travelers_count'] ?></li>
                            <li><strong>تاريخ الإنشاء:</strong> <?= $tour['created_at'] ?></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>تفاصيل إضافية</h5>
                        <ul class="list-unstyled">
                            <li><strong>الإقامة:</strong> <?= htmlspecialchars($tour['accommodation']) ?></li>
                            <li><strong>المطاعم:</strong> <?= htmlspecialchars($tour['restaurants']) ?></li>
                            <li><strong>الأنشطة:</strong> <?= htmlspecialchars($tour['activities']) ?></li>
                            <li><strong>الميزانية التقديرية:</strong> <?= number_format($tour['total_budget'], 2) ?> 
                            ريال</li>
                        </ul>
                    </div>
                    
                </div>

                <hr>

                <div class="tour-details">
                <div class="tour-details">
                <div class="tour-details">
    <h5>تفاصيل البرنامج</h5>
    <div class="card">
        <div class="card-body arabic-content">
            <?php
            $planDetails = $tour['plan_details'];
            // Process text to highlight special sections
            $processedText = preg_replace(
                '/\*\*(.*?):\*\*/',
                '<span class="highlight-header">$1:</span>',
                htmlspecialchars($planDetails)
            );
            echo '<div class="plan-text">';
            echo nl2br($processedText);
            echo '</div>';
            ?>
        </div>
    </div>
</div>
</div>
</div>
            </div>

            <div class="card-footer no-print">
                <div class="d-flex justify-content-between">
                    <div>
                        <button onclick="window.print()" class="btn btn-primary">
                            <i class="bi bi-printer"></i> طباعة الرحلة
                        </button>
                        <a href="edit_tour.php?id=<?= $tour['id'] ?>" class="btn btn-secondary">
                            <i class="bi bi-pencil"></i> تعديل الرحلة
                        </a>
                    </div>
                    <a href="my_tours.php" class="btn btn-outline-secondary">
                        العودة للرحلات
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="dropdown no-print">
    <button class="btn btn-success dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="bi bi-download"></i> تصدير الرحلة
    </button>
    <ul class="dropdown-menu" aria-labelledby="exportDropdown">
        <li>
            <a class="dropdown-item" href="export_tour.php?id=<?= $tour['id'] ?>&format=pdf">
                <i class="bi bi-file-pdf"></i> تصدير PDF
            </a>
        </li>
        <li>
            <a class="dropdown-item" href="export_tour.php?id=<?= $tour['id'] ?>&format=word">
                <i class="bi bi-file-word"></i> تصدير Word
            </a>
        </li>
        <li>
            <a class="dropdown-item" href="export_tour.php?id=<?= $tour['id'] ?>&format=txt">
                <i class="bi bi-file-text"></i> تصدير نص
            </a>
        </li>
    </ul>
</div>


    <!-- Bootstrap JS (اختياري) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- رابط أيقونات Bootstrap (اختياري) -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
</body>
</html>
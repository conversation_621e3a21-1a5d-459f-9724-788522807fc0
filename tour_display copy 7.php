<?php
// تضمين ملفات الإعداد والجلسة
session_start();
require_once 'config.php';
require_once 'session_handler.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من وجود معرف الرحلة
$tourId = $_GET['id'] ?? null;
if (!$tourId) {
    die('لم يتم تحديد معرف الرحلة');
}

// استرجاع بيانات الرحلة من قاعدة البيانات
try {
    $stmt = $db->prepare("SELECT * FROM tour_plans WHERE id = ? AND user_id = ?");
    $stmt->execute([$tourId, $_SESSION['user_id']]);
    $tour = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$tour) {
        die('لم يتم العثور على الرحلة المطلوبة');
    }
} catch (PDOException $e) {
    die('خطأ في قاعدة البيانات: ' . $e->getMessage());
}

function formatTourContent($content) {
    $lines = explode("\n", $content);
    $formattedContent = '';
    $inList = false;
    $dayTitles = [];
    $mainTitleShown = false;

    // تعيين الأيقونات للأنشطة المختلفة
    $activityIcons = [
        'زيارة' => 'fa-landmark',
        'جولة' => 'fa-walking',
        'تناول' => 'fa-utensils',
        'استراحة' => 'fa-coffee',
        'التوجه' => 'fa-car',
        'العودة' => 'fa-home',
        'التسوق' => 'fa-shopping-cart',
        'مشاهدة' => 'fa-eye',
        'رحلة' => 'fa-plane',
        'استكشاف' => 'fa-compass',
        'الإفطار' => 'fa-coffee',
        'الغداء' => 'fa-utensils',
        'العشاء' => 'fa-utensils',
        'default' => 'fa-circle-check'
    ];

    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;

        // إزالة النجوم من بداية الأسطر
        $line = preg_replace('/^\*+\s*/', '', $line);

        // تنسيق العنوان الرئيسي
        if (!$mainTitleShown && preg_match('/برنامج سياحي لمدة \d+ أيام في [^*]+/', $line)) {
            $cleanTitle = preg_replace('/[\*]+/', '', $line);
            $formattedContent .= sprintf('<h2 class="program-title">%s</h2>', 
                htmlspecialchars($cleanTitle)
            );
            $mainTitleShown = true;
            continue;
        }

        // تنسيق عنوان اليوم
        if(preg_match('/(اليوم\s+(?:\d+|الأول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن|التاسع|العاشر):?)/', $line, $matches)) {
            $dayTitle = $matches[1];
            if (!in_array($dayTitle, $dayTitles)) {
                $dayTitles[] = $dayTitle;
                $formattedContent .= sprintf('<h3 class="day-title">%s</h3>', 
                    htmlspecialchars($dayTitle)
                );
            }
            continue;
        }

        // تنسيق أقسام التكلفة والإقامة
        else if(preg_match('/(تقدير.*(?:التكلفة|التكاليف)|التقديرات.*للتكلفة|الإقامة):?/', $line)) {
            $formattedContent .= sprintf('<h4 class="cost-title">%s</h4>', 
                htmlspecialchars($line)
            );
        }

        // تنسيق قسم الملاحظات
        else if(preg_match('/(ملاحظ(?:ات|ة)|ملحوظ(?:ات|ة)):?/', $line)) {
            $formattedContent .= sprintf('<div class="notes-section"><h4 class="notes-title">%s</h4>', 
                htmlspecialchars($line)
            );
        }

        // معالجة الأنشطة مع الوقت
        else if (preg_match('/(\d{2}:\d{2}\s+(?:صباحًا|مساءً|ظهرًا)):\s+(.*)/', $line, $matches)) {
            $time = $matches[1];
            $activity = $matches[2];

            // تحديد الأيقونة المناسبة
            $icon = $activityIcons['default'];
            foreach ($activityIcons as $keyword => $iconClass) {
                if (mb_stripos($activity, $keyword) !== false) {
                    $icon = $iconClass;
                    break;
                }
            }

            // إضافة روابط خرائط جوجل
            $locations = [
                'آيا صوفيا' => 'Hagia+Sophia',
                'المسجد الأزرق' => 'Blue+Mosque',
                'السوق الكبير' => 'Grand+Bazaar+Istanbul',
                'قصر توبكابي' => 'Topkapi+Palace',
                'متحف' => 'Museum',
                'حديقة' => 'Park',
                'شارع' => 'Street',
                'ساحة' => 'Square'
            ];

            foreach ($locations as $place => $mapQuery) {
                if (stripos($activity, $place) !== false) {
                    $mapUrl = "https://www.google.com/maps/search/" . $mapQuery;
                    $activity = str_replace(
                        $place,
                        sprintf('<a href="%s" class="location-link" target="_blank">%s <i class="fa-solid fa-location-dot"></i></a>', $mapUrl, $place),
                        $activity
                    );
                }
            }

            $formattedContent .= sprintf(
                '<li class="timeline-item">
                    <div class="timeline-time">%s</div>
                    <div class="timeline-content">
                        <i class="fa-solid %s activity-icon"></i> %s
                    </div>
                </li>',
                $time,
                $icon,
                $activity
            );
            continue;
        }

        // النص العادي
        if (!$inList) {
            $formattedContent .= sprintf('<p class="tour-text">%s</p>', $line);
        } else {
            $formattedContent .= sprintf(
                '<li class="timeline-item">
                    <div class="timeline-content">
                        <i class="fa-solid fa-circle-check activity-icon"></i> %s
                    </div>
                </li>', 
                $line
            );
        }
    }

    return $formattedContent;
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الرحلة - <?= htmlspecialchars($tour['city']) ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@500;700&display=swap">
    <style>
        .arabic-content {
            direction: rtl;
            text-align: right;
            font-family: 'Tajawal', sans-serif;
            line-height: 1.6;
            font-size: 1.1em;
            font-weight: 500;
            padding: 20px;
        }

        .tour-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
        }

        .tour-header {
            background-color: #f8f9fa;
            padding: 25px;
            margin-bottom: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .tour-title {
            color: #2c3e50;
            font-size: 2.2em;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .program-title {
            text-align: center;
            font-size: 1.8em;
            color: #2c3e50;
            background-color: rgba(52, 152, 219, 0.1);
            padding: 20px;
            margin: 30px auto;
            border-radius: 10px;
            font-weight: bold;
            max-width: 800px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .day-title {
            color: #2c3e50;
            font-size: 1.8em;
            margin: 30px 0 20px;
            padding: 15px;
            background: rgba(52, 152, 219, 0.1);
            border-radius: 8px;
            border-right: 4px solid #3498db;
        }

        .timeline {
            list-style: none;
            padding: 0;
            position: relative;
        }

        .timeline:before {
            content: '';
            position: absolute;
            top: 0;
            right: 20px;
            height: 100%;
            width: 2px;
            background: #e9ecef;
        }

        .timeline-item {
            margin-bottom: 20px;
            position: relative;
            padding-right: 50px;
        }

        .timeline-item:before {
            content: '';
            position: absolute;
            right: 16px;
            top: 0;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #3498db;
            border: 2px solid #fff;
        }

        .activity-icon {
            margin-left: 10px;
            color: #3498db;
            font-size: 1.2em;
            width: 20px;
            text-align: center;
        }

        .timeline-content {
            display: flex;
            align-items: flex-start;
            background: #fff;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            font-size: 1.1em;
            line-height: 1.6;
        }

        .timeline-time {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
            font-size: 1.15em;
        }

        .location-link {
            display: inline-flex;
            align-items: center;
            color: #3498db;
            text-decoration: none;
            transition: all 0.3s;
            padding: 4px 8px;
            border-radius: 4px;
            background-color: #f8f9fa;
            margin: 0 5px;
        }

        .location-link:hover {
            background-color: #e9ecef;
            color: #2980b9;
            transform: translateY(-2px);
        }

        .location-link i {
            color: #e74c3c;
            margin-right: 5px;
        }

        .notes-section {
            background: #f7f9fc;
            padding: 25px;
            border-radius: 15px;
            margin: 30px 0;
            border-right: 5px solid #3498db;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .notes-title {
            color: #2c3e50;
            font-size: 1.5em;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .cost-title {
            color: #27ae60;
            font-size: 1.5em;
            margin: 25px 0 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #27ae60;
        }

        .meta-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .meta-item {
            background: #fff;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .meta-label {
            color: #3498db;
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 1.1em;
        }

        @media print {
            .no-print {
                display: none !important;
            }
            
            .tour-container {
                box-shadow: none;
                margin: 0;
                padding: 0;
            }
            
            .timeline:before {
                display: none;
            }
            
            .timeline-item:before {
                display: none;
            }
        }
    </style>
</head>
<body class="bg-light">
    <div class="tour-container arabic-content">
        <div class="tour-header">
            <h1 class="tour-title"><?= htmlspecialchars($tour['city']) ?></h1>
            
            <?php if(!empty($tour['map_url'])): ?>
                <a href="<?= htmlspecialchars($tour['map_url']) ?>" target="_blank" class="btn btn-primary mb-3">
                <i class="fas fa-map-marker-alt"></i> عرض على الخريطة
                </a>
            <?php endif; ?>

            <div class="meta-section">
                <div class="meta-item">
                    <div class="meta-label"><i class="fas fa-city"></i> المدينة</div>
                    <?= htmlspecialchars($tour['city']) ?>
                </div>
                <div class="meta-item">
                    <div class="meta-label"><i class="fas fa-calendar-alt"></i> المدة</div>
                    <?= $tour['days_count'] ?> أيام
                </div>
                <div class="meta-item">
                    <div class="meta-label"><i class="fas fa-users"></i> عدد المسافرين</div>
                    <?= $tour['travelers_count'] ?> أشخاص
                </div>
                <div class="meta-item">
                    <div class="meta-label"><i class="fas fa-clock"></i> تاريخ الإنشاء</div>
                    <?= date('Y-m-d', strtotime($tour['created_at'])) ?>
                </div>
                <div class="meta-item">
                    <div class="meta-label"><i class="fas fa-hotel"></i> الإقامة</div>
                    <?= htmlspecialchars($tour['accommodation']) ?>
                </div>
                <div class="meta-item">
                    <div class="meta-label"><i class="fas fa-utensils"></i> المطاعم</div>
                    <?= htmlspecialchars($tour['restaurants']) ?>
                </div>
                <div class="meta-item">
                    <div class="meta-label"><i class="fas fa-hiking"></i> الأنشطة</div>
                    <?= htmlspecialchars($tour['activities']) ?>
                </div>
                <div class="meta-item">
                    <div class="meta-label"><i class="fas fa-wallet"></i> الميزانية التقديرية</div>
                    <?= number_format($tour['total_budget'], 2) ?> ريال
                </div>
            </div>
        </div>

        <div class="tour-content">
            <?php echo formatTourContent($tour['plan_details']); ?>
        </div>

        <?php if(!empty($tour['notes'])): ?>
        <div class="notes-section">
            <h4 class="notes-title"><i class="fas fa-sticky-note"></i> ملاحظات مهمة</h4>
            <div class="notes-content">
                <?php echo formatTourContent($tour['notes']); ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- شريط الأدوات -->
        <div class="action-toolbar no-print mt-4 mb-4">
            <div class="row">
                <div class="col-md-8">
                    <div class="btn-group" role="group">
                        <button onclick="window.print()" class="btn btn-primary">
                            <i class="fas fa-print"></i> طباعة الرحلة
                        </button>
                        
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-download"></i> تصدير الرحلة
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="export_tour.php?id=<?= $tour['id'] ?>&format=pdf">
                                        <i class="fas fa-file-pdf"></i> تصدير PDF
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="export_tour.php?id=<?= $tour['id'] ?>&format=word">
                                        <i class="fas fa-file-word"></i> تصدير Word
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="export_tour.php?id=<?= $tour['id'] ?>&format=txt">
                                        <i class="fas fa-file-alt"></i> تصدير نص
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <a href="edit_tour.php?id=<?= $tour['id'] ?>" class="btn btn-secondary">
                            <i class="fas fa-edit"></i> تعديل الرحلة
                        </a>
                        
                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#shareModal">
                            <i class="fas fa-share-alt"></i> مشاركة
                        </button>
                    </div>
                </div>
                
                <div class="col-md-4 text-end">
                    <a href="my_tours.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للرحلات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal مشاركة الرحلة -->
    <div class="modal fade" id="shareModal" tabindex="-1" aria-labelledby="shareModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="shareModalLabel">مشاركة الرحلة</h5>
                    <button type="button" class="btn-close ms-0 me-auto" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">رابط الرحلة:</label>
                        <div class="input-group">
                            <input type="text" class="form-control" value="<?= htmlspecialchars("https://{$_SERVER['HTTP_HOST']}/tour.php?id={$tour['id']}") ?>" id="tourLink" readonly>
                            <button class="btn btn-outline-primary" onclick="copyToClipboard('tourLink')">
                                <i class="fas fa-copy"></i> نسخ
                            </button>
                        </div>
                    </div>
                    <div class="share-buttons text-center mt-4">
                        <a href="#" class="btn btn-primary mx-2"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="btn btn-info mx-2"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="btn btn-success mx-2"><i class="fab fa-whatsapp"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript لنسخ الرابط -->
    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            element.select();
            document.execCommand('copy');
            
            // إظهار رسالة نجاح
            const button = element.nextElementSibling;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                button.innerHTML = originalText;
            }, 2000);
        }
    </script>

    <!-- JavaScript مكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
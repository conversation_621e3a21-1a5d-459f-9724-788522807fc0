<?php
// get_cities.php
header('Content-Type: text/html; charset=utf-8');

$servername = "localhost";
$username = "root";
$password = "";
$dbname = "tourism_db";

// إنشاء الاتصال
$conn = new mysqli($servername, $username, $password, $dbname);

// التحقق من الاتصال
if ($conn->connect_error) {
    echo "<option value=''>خطأ في الاتصال: " . $conn->connect_error . "</option>";
    exit();
}

// تعيين الترميز
$conn->set_charset("utf8mb4");

if (isset($_POST['country_id']) && !empty($_POST['country_id'])) {
    $country_id = intval($_POST['country_id']);

    $sql = "SELECT id, name FROM cities WHERE country_id = ? ORDER BY name";
    $stmt = $conn->prepare($sql);

    if ($stmt) {
        $stmt->bind_param("i", $country_id);
        $stmt->execute();
        $result = $stmt->get_result();

        echo "<option value=''>اختر المدينة</option>";

        if ($result->num_rows > 0) {
            while($row = $result->fetch_assoc()) {
                echo "<option value='".$row['id']."'>".$row['name']."</option>";
            }
        } else {
            echo "<option value=''>لا توجد مدن متاحة لهذه الدولة</option>";
        }

        $stmt->close();
    } else {
        echo "<option value=''>خطأ في إعداد الاستعلام</option>";
    }
} else {
    echo "<option value=''>معرف الدولة مطلوب</option>";
}

$conn->close();
?>
<?php
// get_cities.php
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "tourism_db";

$conn = new mysqli($servername, $username, $password, $dbname);

if (isset($_POST['country_id'])) {
    $country_id = $_POST['country_id'];
    $sql = "SELECT * FROM cities WHERE country_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $country_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    echo "<option value=''>اختر المدينة</option>";
    while($row = $result->fetch_assoc()) {
        echo "<option value='".$row['name']."'>".$row['name']."</option>";
    }
}

$conn->close();
?>
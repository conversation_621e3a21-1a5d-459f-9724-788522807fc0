<?php
header('Content-Type: text/html; charset=utf-8');
mb_internal_encoding('UTF-8');
require_once 'config.php';
require_once 'session_handler.php';
requireLogin();

// التحقق من معرف الرحلة
$tourId = $_GET['id'] ?? 0;

// جلب تفاصيل الرحلة
$stmt = $db->prepare("SELECT * FROM tour_plans WHERE id = ? AND user_id = ?");
$stmt->execute([$tourId, $_SESSION['user_id']]);
$tour = $stmt->fetch();

if (!$tour) {
    // توجيه في حال عدم وجود الرحلة
    header("Location: my_tours.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الرحلة</title>
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css">
    <style>
        @media print {
            .no-print {
                display: none;
            }
        }
    </style>
    <style>
        /* body {
            font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
            line-height: 1.8;
        } */
        .tour-details {
            
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        .day-header {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .activity-item {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
    </style>
    <!-- Navbar متكامل -->
<nav class="navbar navbar-expand-lg navbar-light bg-light shadow-sm">
    <div class="container">
        <!-- شعار الموقع -->
        <a class="navbar-brand" href="index.php">
            <i class="bi bi-airplane-fill text-primary"></i> مخطط الرحلات
        </a>
        
        <!-- زر التبديل للشاشات الصغيرة -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavbar">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <!-- محتوى القائمة -->
        <div class="collapse navbar-collapse" id="mainNavbar">
            <!-- روابط القائمة اليمين -->
            <div class="navbar-nav me-auto">
                <a class="nav-item nav-link" href="index.php">
                    <i class="bi bi-house me-1"></i> الرئيسية
                </a>
                <a class="nav-item nav-link" href="preferences.php">
                    <i class="bi bi-plus-circle me-1"></i> إنشاء رحلة
                </a>
                <a class="nav-item nav-link" href="my_tours.php">
                    <i class="bi bi-list-check me-1"></i> رحلاتي
                </a>
            </div>
            
            <!-- روابط تسجيل الدخول -->
            <div class="navbar-nav">
                <?php if(isset($_SESSION['user_id'])): ?>
                    <!-- قائمة المستخدم المسجل -->
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i> 
                            <?= htmlspecialchars($_SESSION['username']) ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li>
                                <a class="dropdown-item" href="profile.php">
                                    <i class="bi bi-person me-1"></i> الملف الشخصي
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="settings.php">
                                    <i class="bi bi-gear me-1"></i> الإعدادات
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger" href="login.php?logout=1">
                                    <i class="bi bi-box-arrow-left me-1"></i> تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </div>
                <?php else: ?>
                    <!-- روابط تسجيل الدخول للزوار -->
                    <a class="nav-item nav-link" href="login.php">
                        <i class="bi bi-box-arrow-in-right me-1"></i> تسجيل الدخول
                    </a>
                    <a class="nav-item nav-link" href="register.php">
                        <i class="bi bi-person-plus me-1"></i> إنشاء حساب
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</nav>

<!-- CSS إضافي -->
<style>
    .navbar {
        margin-bottom: 20px;
        background-color: #f8f9fa !important;
    }
    .nav-link {
        transition: all 0.3s ease;
    }
    .nav-link:hover {
        color: #3498db !important;
        transform: translateY(-2px);
    }
    .dropdown-menu {
        min-width: 200px;
    }
</style>
</head>
<body>
    <div class="container my-5">
        <div class="card">
            <div class="card-header">
                <h3>تفاصيل رحلة <?= htmlspecialchars($tour['city']) ?></h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>معلومات الرحلة الأساسية</h5>
                        <ul class="list-unstyled">
                            <li><strong>المدينة:</strong> <?= htmlspecialchars($tour['city']) ?></li>
                            <li><strong>المدة:</strong> <?= $tour['days_count'] ?> أيام</li>
                            <li><strong>عدد المسافرين:</strong> <?= $tour['travelers_count'] ?></li>
                            <li><strong>تاريخ الإنشاء:</strong> <?= $tour['created_at'] ?></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>تفاصيل إضافية</h5>
                        <ul class="list-unstyled">
                            <li><strong>الإقامة:</strong> <?= htmlspecialchars($tour['accommodation']) ?></li>
                            <li><strong>المطاعم:</strong> <?= htmlspecialchars($tour['restaurants']) ?></li>
                            <li><strong>الأنشطة:</strong> <?= htmlspecialchars($tour['activities']) ?></li>
                            <li><strong>الميزانية التقديرية:</strong> <?= number_format($tour['total_budget'], 2) ?> 
                            ريال</li>
                        </ul>
                    </div>
                    
                </div>

                <hr>

                <div class="tour-details">
                    <h5>تفاصيل البرنامج</h5>
                    <div class="card">
                        <div class="card-body">
                            <pre class="text-right"><?= htmlspecialchars($tour['plan_details']) ?></pre>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-footer no-print">
                <div class="d-flex justify-content-between">
                    <div>
                        <button onclick="window.print()" class="btn btn-primary">
                            <i class="bi bi-printer"></i> طباعة الرحلة
                        </button>
                        <a href="edit_tour.php?id=<?= $tour['id'] ?>" class="btn btn-secondary">
                            <i class="bi bi-pencil"></i> تعديل الرحلة
                        </a>
                    </div>
                    <a href="my_tours.php" class="btn btn-outline-secondary">
                        العودة للرحلات
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="dropdown no-print">
    <button class="btn btn-success dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="bi bi-download"></i> تصدير الرحلة
    </button>
    <ul class="dropdown-menu" aria-labelledby="exportDropdown">
        <li>
            <a class="dropdown-item" href="export_tour.php?id=<?= $tour['id'] ?>&format=pdf">
                <i class="bi bi-file-pdf"></i> تصدير PDF
            </a>
        </li>
        <li>
            <a class="dropdown-item" href="export_tour.php?id=<?= $tour['id'] ?>&format=word">
                <i class="bi bi-file-word"></i> تصدير Word
            </a>
        </li>
        <li>
            <a class="dropdown-item" href="export_tour.php?id=<?= $tour['id'] ?>&format=txt">
                <i class="bi bi-file-text"></i> تصدير نص
            </a>
        </li>
    </ul>
</div>
<div class="container py-5">
        <div class="card">
            <div class="card-header">
                <h2 class="text-center">تفاصيل رحلة <?= htmlspecialchars($tour['city']) ?></h2>
            </div>
            <div class="card-body tour-details">
            <?= htmlspecialchars($tour['plan_details']) ?>
                
                <?php
                // معالجة النص لعرضه بشكل صحيح
                $planDetails = $tour['plan_details'];

                // تقسيم النص إلى أيام
                $days = preg_split('/\*\*اليوم \d+:\*\*/', $planDetails);
                array_shift($days); // إزالة العنصر الأول الفارغ

                // عنوان كل يوم
                preg_match_all('/\*\*اليوم \d+:\*\*/', $planDetails, $dayTitles);

                // عرض كل يوم
                foreach ($days as $index => $dayContent) {
                    echo "<div class='day-section'>";
                    echo "<div class='day-header'>";
                    echo "<h4>" . trim($dayTitles[0][$index]) . "</h4>";
                    echo "</div>";

                    // تقسيم النشاطات
                    $activities = explode('*', $dayContent);
                    array_shift($activities); // إزالة العنصر الأول الفارغ

                    echo "<div class='activities-list'>";
                    foreach ($activities as $activity) {
                        echo "<div class='activity-item'>";
                        echo "<p>" . trim($activity) . "</p>";
                        echo "</div>";
                    }
                    echo "</div>";
                    echo "</div>";
                }

                // عرض الملاحظات
                $notes = strstr($planDetails, '**ملحوظات:**');
                if ($notes) {
                    echo "<div class='notes-section mt-4'>";
                    echo "<h4>ملاحظات هامة</h4>";
                    echo "<div class='alert alert-info'>" . nl2br(htmlspecialchars($notes)) . "</div>";
                    echo "</div>";
                }
                ?>
            </div>
            <div class="card-footer text-center">
                <a href="my_tours.php" class="btn btn-secondary me-2">رجوع للرحلات</a>
                <button onclick="window.print()" class="btn btn-primary">طباعة الرحلة</button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS (اختياري) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- رابط أيقونات Bootstrap (اختياري) -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
</body>
</html>
<?php
require_once 'config.php';
require_once 'session_handler.php';
requireLogin();

header('Content-Type: application/json');

// التحقق من معرف الرحلة
$tourId = $_GET['id'] ?? 0;

try {
    // التأكد من أن الرحلة للمستخدم الحالي
    $stmt = $db->prepare("DELETE FROM tour_plans WHERE id = ? AND user_id = ?");
    $result = $stmt->execute([$tourId, $_SESSION['user_id']]);

    if ($result && $stmt->rowCount() > 0) {
        echo json_encode([
            'success' => true,
            'message' => 'تم حذف الرحلة بنجاح'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'لم يتم العثور على الرحلة أو ليس لديك صلاحية الحذف'
        ]);
    }
} catch(PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ أثناء حذف الرحلة: ' . $e->getMessage()
    ]);
}
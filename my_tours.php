<?php
require_once 'config.php';
require_once 'session_handler.php';
requireLogin();

// جلب الرحلات الخاصة بالمستخدم
$stmt = $db->prepare("SELECT * FROM tour_plans WHERE user_id = ? ORDER BY created_at DESC");
$stmt->execute([$_SESSION['user_id']]);
$tours = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>رحلاتي</title>
    
    <!-- Bootstrap 5.3 CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f4f6f9;
        }
        .tour-card {
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .tour-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 10px 15px rgba(0,0,0,0.15);
        }
        .card-header {
            background-color: #f8f9fa;
        }
        .btn-custom {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }
    </style>
</head>
<!-- Navbar متكامل -->
<nav class="navbar navbar-expand-lg navbar-light bg-light shadow-sm">
    <div class="container">
        <!-- شعار الموقع -->
        <a class="navbar-brand" href="index.php">
            <i class="bi bi-airplane-fill text-primary"></i> مخطط الرحلات
        </a>
        
        <!-- زر التبديل للشاشات الصغيرة -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavbar">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <!-- محتوى القائمة -->
        <div class="collapse navbar-collapse" id="mainNavbar">
            <!-- روابط القائمة اليمين -->
            <div class="navbar-nav me-auto">
                <a class="nav-item nav-link" href="index.php">
                    <i class="bi bi-house me-1"></i> الرئيسية
                </a>
                <a class="nav-item nav-link" href="preferences.php">
                    <i class="bi bi-plus-circle me-1"></i> إنشاء رحلة
                </a>
                <a class="nav-item nav-link" href="my_tours.php">
                    <i class="bi bi-list-check me-1"></i> رحلاتي
                </a>
            </div>
            
            <!-- روابط تسجيل الدخول -->
            <div class="navbar-nav">
                <?php if(isset($_SESSION['user_id'])): ?>
                    <!-- قائمة المستخدم المسجل -->
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i> 
                            <?= htmlspecialchars($_SESSION['username']) ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li>
                                <a class="dropdown-item" href="profile.php">
                                    <i class="bi bi-person me-1"></i> الملف الشخصي
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="settings.php">
                                    <i class="bi bi-gear me-1"></i> الإعدادات
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger" href="login.php?logout=1">
                                    <i class="bi bi-box-arrow-left me-1"></i> تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </div>
                <?php else: ?>
                    <!-- روابط تسجيل الدخول للزوار -->
                    <a class="nav-item nav-link" href="login.php">
                        <i class="bi bi-box-arrow-in-right me-1"></i> تسجيل الدخول
                    </a>
                    <a class="nav-item nav-link" href="register.php">
                        <i class="bi bi-person-plus me-1"></i> إنشاء حساب
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</nav>

<!-- CSS إضافي -->
<style>
    .navbar {
        margin-bottom: 20px;
        background-color: #f8f9fa !important;
    }
    .nav-link {
        transition: all 0.3s ease;
    }
    .nav-link:hover {
        color: #3498db !important;
        transform: translateY(-2px);
    }
    .dropdown-menu {
        min-width: 200px;
    }
</style>
<body>


    <div class="container">
        <div class="row mb-4 align-items-center">
            <div class="col">
                <h2 class="mb-0">
                    <i class="bi bi-compass text-primary me-2"></i>رحلاتي السابقة
                </h2>
            </div>
            <div class="col-auto">
                <a href="preferences.php" class="btn btn-primary btn-custom">
                    <i class="bi bi-plus"></i> إنشاء رحلة جديدة
                </a>
            </div>
        </div>

        <?php if(empty($tours)): ?>
            <div class="alert alert-info d-flex align-items-center" role="alert">
                <i class="bi bi-info-circle me-2"></i>
                <div>لم تقم بإنشاء أي رحلات حتى الآن</div>
            </div>
        <?php else: ?>
            <div class="row row-cols-1 row-cols-md-3 g-4">
                <?php foreach($tours as $tour): ?>
                    <div class="col tour-card-container">
                        <div class="card tour-card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-geo-alt text-primary me-2"></i>
                                    <?= htmlspecialchars($tour['city']) ?>
                                </h5>
                                <span class="badge bg-secondary">
                                    <?= $tour['days_count'] ?> أيام
                                </span>
                            </div>
                            <div class="card-body">
                                <p class="card-text">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>
                                            <i class="bi bi-people text-muted me-2"></i>
                                            عدد المسافرين
                                        </span>
                                        <strong><?= $tour['travelers_count'] ?></strong>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>
                                            <i class="bi bi-calendar-check text-muted me-2"></i>
                                            تاريخ الإنشاء
                                        </span>
                                        <strong><?= date('Y-m-d', strtotime($tour['created_at'])) ?></strong>
                                    </div>
                                </p>
                            </div>
                            <div class="card-footer bg-transparent d-flex justify-content-between">
                                <a href="tour_display.php?id=<?= $tour['id'] ?>" class="btn btn-sm btn-outline-primary btn-custom">
                                    <i class="bi bi-eye"></i> عرض التفاصيل
                                </a>
                                <button class="btn btn-sm btn-outline-danger delete-tour btn-custom" data-id="<?= $tour['id'] ?>">
                                    <i class="bi bi-trash"></i> حذف
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Bootstrap 5.3 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const deleteButtons = document.querySelectorAll('.delete-tour');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const tourId = this.getAttribute('data-id');
                
                Swal.fire({
                    title: 'هل أنت متأكد؟',
                    text: "لن تتمكن من استعادة هذه الرحلة!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'نعم، احذف!',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // إرسال طلب حذف عبر AJAX
                        fetch(`delete_tour.php?id=${tourId}`, {
                            method: 'DELETE'
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // إزالة العنصر من الصفحة
                                const cardContainer = this.closest('.tour-card-container');
                                
                                // تأثير الإزالة
                                cardContainer.classList.add('animate__animated', 'animate__fadeOut');
                                
                                setTimeout(() => {
                                    cardContainer.remove();
                                    
                                    // التحقق من وجود رحلات
                                    const remainingTours = document.querySelectorAll('.tour-card-container');
                                    if (remainingTours.length === 0) {
                                        const container = document.querySelector('.container');
                                        container.innerHTML = `
                                            <div class="alert alert-info d-flex align-items-center" role="alert">
                                                <i class="bi bi-info-circle me-2"></i>
                                                <div>لم تقم بإنشاء أي رحلات حتى الآن</div>
                                            </div>
                                        `;
                                    }
                                }, 500);

                                Swal.fire({
                                    title: 'تم الحذف!',
                                    text: 'تمت إزالة الرحلة بنجاح.',
                                    icon: 'success',
                                    timer: 2000,
                                    showConfirmButton: false
                                });
                            } else {
                                Swal.fire({
                                    title: 'خطأ!',
                                    text: data.message || 'حدث خطأ أثناء الحذف',
                                    icon: 'error',
                                    confirmButtonText: 'حسناً'
                                });
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            Swal.fire({
                                title: 'خطأ في الاتصال!',
                                text: 'تعذر الاتصال بالخادم',
                                icon: 'error',
                                confirmButtonText: 'حسناً'
                            });
                        });
                    }
                });
            });
        });

        // إضافة تأثيرات للبطاقات
        const tourCards = document.querySelectorAll('.tour-card');
        tourCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.classList.add('shadow-lg');
            });
            
            card.addEventListener('mouseleave', function() {
                this.classList.remove('shadow-lg');
            });
        });
    });

    // دالة إضافية للتحقق من حالة الصفحة
    function checkPageState() {
        const tourCount = document.querySelectorAll('.tour-card-container').length;
        const container = document.querySelector('.container');

        if (tourCount === 0 && !container.querySelector('.alert-info')) {
            container.innerHTML = `
                <div class="alert alert-info d-flex align-items-center" role="alert">
                    <i class="bi bi-info-circle me-2"></i>
                    <div>لم تقم بإنشاء أي رحلات حتى الآن</div>
                </div>
            `;
        }
    }

    // إضافة مكتبة Animate.css للتأثيرات
    const animateCss = document.createElement('link');
    animateCss.rel = 'stylesheet';
    animateCss.href = 'https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css';
    document.head.appendChild(animateCss);
    </script>
</body>
</html>
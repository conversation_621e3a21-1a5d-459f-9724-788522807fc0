<?php
require_once 'config.php';
require_once 'session_handler.php';
requireLogin();

// جلب الرحلات الخاصة بالمستخدم
$stmt = $db->prepare("SELECT * FROM tour_plans WHERE user_id = ? ORDER BY created_at DESC");
$stmt->execute([$_SESSION['user_id']]);
$tours = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <title>رحلاتي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container my-5">
        <h2 class="mb-4">رحلاتي السابقة</h2>

        <?php if(empty($tours)): ?>
            <div class="alert alert-info">
                لم تقم بإنشاء أي رحلات حتى الآن
            </div>
        <?php else: ?>
            <div class="row">
                <?php foreach($tours as $tour): ?>
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title"><?= htmlspecialchars($tour['city']) ?></h5>
                                <p class="card-text">
                                    <strong>المدة:</strong> <?= $tour['days_count'] ?> أيام<br>
                                    <strong>عدد المسافرين:</strong> <?= $tour['travelers_count'] ?><br>
                                    <strong>تاريخ الإنشاء:</strong> <?= date('Y-m-d', strtotime($tour['created_at'])) ?>
                                </p>
                                <div class="d-flex justify-content-between">
                                    <a href="view_tour.php?id=<?= $tour['id'] ?>" class="btn btn-primary btn-sm">
                                        عرض التفاصيل
                                    </a>
                                    <button class="btn btn-danger btn-sm delete-tour" data-id="<?= $tour['id'] ?>">
                                        حذف
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // معالجة حذف الرحلة
        const deleteButtons = document.querySelectorAll('.delete-tour');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const tourId = this.getAttribute('data-id');
                
                Swal.fire({
                    title: 'هل أنت متأكد?',
                    text: "لن تتمكن من استعادة هذه الرحلة!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'نعم، احذف!',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // إرسال طلب حذف عبر AJAX
                        fetch(`delete_tour.php?id=${tourId}`, {
                            method: 'DELETE'
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // إزالة العنصر من الصفحة
                                this.closest('.col-md-4').remove();
                                Swal.fire(
                                    'تم الحذف!',
                                    'تمت إزالة الرحلة بنجاح.',
                                    'success'
                                );
                            } else {
                                Swal.fire(
                                    'خطأ!',
                                    data.message || 'حدث خطأ أثناء الحذف',
                                    'error'
                                );
                            }
                        });
                    }
                });
            });
        });
    });
    </script>
</body>
</html>
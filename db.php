<?php
// Database configuration
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "tourism_db";

// Create database connection
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Create necessary tables
$sql = "
CREATE TABLE IF NOT EXISTS countries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100)
);

CREATE TABLE IF NOT EXISTS cities (
    id INT PRIMARY KEY AUTO_INCREMENT,
    country_id INT,
    name VARCHAR(100),
    FOREIGN KEY (country_id) REFERENCES countries(id)
);

-- Insert sample data
INSERT INTO countries (name) VALUES 
('UAE'), ('Japan'), ('France'), ('Italy'), ('Thailand');

INSERT INTO cities (country_id, name) VALUES 
(1, 'Dubai'), (1, 'Abu Dhabi'),
(2, 'Tokyo'), (2, 'Kyoto'),
(3, 'Paris'), (3, 'Nice'),
(4, 'Rome'), (4, 'Venice'),
(5, 'Bangkok'), (5, 'Phuket');
";

$conn->multi_query($sql);
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>برنامج السياحة العالمية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Arial', sans-serif; padding: 20px; }
        .form-container { max-width: 800px; margin: 0 auto; }
    </style>
</head>
<body>
    <div class="container form-container">
        <h2 class="text-center mb-4">برنامج السياحة العالمية</h2>
        
        <form id="tourForm" action="preferences.php" method="POST">
            <div class="mb-3">
                <label class="form-label">اختر الدولة:</label>
                <select class="form-select" name="country" id="countrySelect" required>
                    <option value="">اختر الدولة</option>
                    <?php
                    $result = $conn->query("SELECT * FROM countries");
                    while($row = $result->fetch_assoc()) {
                        echo "<option value='".$row['id']."'>".$row['name']."</option>";
                    }
                    ?>
                </select>
            </div>

            <div class="mb-3">
                <label class="form-label">اختر المدينة:</label>
                <select class="form-select" name="city" id="citySelect" required>
                    <option value="">اختر المدينة</option>
                </select>
            </div>

            <div class="mb-3">
                <label class="form-label">عدد المسافرين:</label>
                <select class="form-select" name="travelers" required>
                    <?php for($i=1; $i<=10; $i++) { ?>
                        <option value="<?php echo $i; ?>"><?php echo $i; ?></option>
                    <?php } ?>
                </select>
            </div>

            <div class="text-center">
                <button type="submit" class="btn btn-primary">عمل برنامج سياحي</button>
            </div>
        </form>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $('#countrySelect').change(function() {
            var countryId = $(this).val();
            $.ajax({
                url: 'get_cities.php',
                type: 'POST',
                data: {country_id: countryId},
                success: function(response) {
                    $('#citySelect').html(response);
                }
            });
        });
    </script>
</body>
</html>
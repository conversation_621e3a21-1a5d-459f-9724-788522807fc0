<?php
require_once 'config.php';

try {
    // Get 5 random approved testimonials
    $stmt = $db->prepare("
        SELECT name, city, content 
        FROM testimonials 
        WHERE status = 'approved' 
        ORDER BY RAND() 
        LIMIT 5
    ");
    $stmt->execute();
    $random_testimonials = $stmt->fetchAll();
} catch(PDOException $e) {
    error_log("Error fetching random testimonials: " . $e->getMessage());
    $random_testimonials = [];
}
?>

<section class="testimonials-section py-1 bg-light">
    <div class="container">
        <h2 class="text-center mb-12">آراء عملائنا</h2>
        
        <div id="testimonialCarousel" class="carousel slide" data-bs-ride="carousel">
        <i class="bi bi-quote display-6 text-primary"></i>
            <div class="carousel-inner">
            
                <?php foreach($random_testimonials as $index => $item): ?>
                    <div class="carousel-item <?= $index === 0 ? 'active' : '' ?>">
                        <div class="card border-0 bg-transparent">
                            <div class="card-body text-center">
                                <p class="lead mb-4"><?= htmlspecialchars($item['content']) ?></p>
                                <footer class="blockquote-footer">
                                    <strong><?= htmlspecialchars($item['name']) ?></strong>
                                    من <?= htmlspecialchars($item['city']) ?>
                                </footer>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- <button class="carousel-control-prev" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon"></span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon"></span>
            </button> -->
        </div>
    </div>
</section>

<style>
.testimonials-section {
    background: colorrgb(255, 255, 255);
}

.carousel-item {
    min-height: 50px;
    padding: 1px;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    background-color: #000111;
    border-radius: 100%;
    padding: 10px;
}
</style>
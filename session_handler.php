<?php
// session_handler.php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// وظائف إضافية لإدارة الجلسات
function isUserLoggedIn() {
    return isset($_SESSION['user_id']);
}

function getCurrentUserId() {
    return $_SESSION['user_id'] ?? null;
}

function requireLogin() {
    if (!isUserLoggedIn()) {
        // تخزين الرابط الحالي للعودة إليه بعد تسجيل الدخول
        $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
        header("Location: login.php");
        exit();
    }
}

function setFlashMessage($type, $message) {
    $_SESSION['flash_message'] = [
        'type' => $type,
        'message' => $message
    ];
}

function displayFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $flash = $_SESSION['flash_message'];
        $alertClass = $flash['type'] == 'error' ? 'alert-danger' : 'alert-success';
        
        echo "<div class='alert {$alertClass}'>{$flash['message']}</div>";
        
        // مسح الرسالة بعد عرضها
        unset($_SESSION['flash_message']);
    }
}
<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once '../config.php';
require_once 'auth_check.php';

// Add country handler
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_country'])) {
    try {
        $countryName = trim($_POST['country_name']);
        
        if (empty($countryName)) {
            throw new Exception('اسم الدولة مطلوب');
        }

        // Check if exists
        $checkStmt = $db->prepare("SELECT COUNT(*) FROM countries WHERE name = ?");
        $checkStmt->execute([$countryName]);
        if ($checkStmt->fetchColumn() > 0) {
            throw new Exception('هذه الدولة موجودة مسبقاً');
        }

        // Insert new country
        $stmt = $db->prepare("INSERT INTO countries (name) VALUES (?)");
        $stmt->execute([$countryName]);
        
        $_SESSION['success'] = 'تمت إضافة الدولة بنجاح';
        header('Location: countries.php');
        exit;

    } catch (Exception $e) {
        $_SESSION['error'] = $e->getMessage();
    }
}

// Add debug logging at top of file
error_log("POST Data: " . print_r($_POST, true));

// Add at top of file after requires
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_country'])) {
    try {
        $countryName = trim($_POST['country_name']);
        error_log("Adding country: " . $countryName); // Debug log
        
        if (empty($countryName)) {
            throw new Exception('اسم الدولة مطلوب');
        }

        // Check if exists
        $checkStmt = $db->prepare("SELECT COUNT(*) FROM countries WHERE name = ?");
        $checkStmt->execute([$countryName]);
        if ($checkStmt->fetchColumn() > 0) {
            throw new Exception('هذه الدولة موجودة مسبقاً');
        }

        // Insert new country
        $stmt = $db->prepare("INSERT INTO countries (name) VALUES (?)");
        $result = $stmt->execute([$countryName]);
        
        if ($result) {
            $_SESSION['success'] = 'تمت إضافة الدولة بنجاح';
            header('Location: countries.php');
            exit;
        } else {
            throw new Exception('فشل في إضافة الدولة');
        }

    } catch (Exception $e) {
        error_log("Error adding country: " . $e->getMessage());
        $_SESSION['error'] = $e->getMessage();
    }
}

// Handle country deletion
if (isset($_POST['delete_country'])) {
    try {
        $stmt = $db->prepare("DELETE FROM countries WHERE id = ?");
        $stmt->execute([$_POST['country_id']]);
        $_SESSION['success'] = 'تم حذف الدولة بنجاح';
    } catch(PDOException $e) {
        $_SESSION['error'] = 'لا يمكن حذف الدولة لوجود مدن مرتبطة بها';
    }
}

// Add after delete handler
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['edit_country'])) {
    try {
        $countryId = (int)$_POST['country_id'];
        $countryName = trim($_POST['country_name']);
        
        if (empty($countryName)) {
            throw new Exception('اسم الدولة مطلوب');
        }

        $stmt = $db->prepare("UPDATE countries SET name = ? WHERE id = ?");
        $stmt->execute([$countryName, $countryId]);
        
        $_SESSION['success'] = 'تم تحديث الدولة بنجاح';
        header('Location: countries.php');
        exit;
    } catch (Exception $e) {
        $_SESSION['error'] = $e->getMessage();
    }
}

// Get countries with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

try {
    // Get total count
    $countStmt = $db->prepare("SELECT COUNT(*) FROM countries WHERE name LIKE ?");
    $countStmt->execute(["%$search%"]);
    $total = $countStmt->fetchColumn();
    $totalPages = ceil($total / $limit);
    
    // Get countries with city count
    $stmt = $db->prepare("
        SELECT 
            c.id,
            c.name,
            COUNT(ct.id) as city_count
        FROM countries c
        LEFT JOIN cities ct ON c.id = ct.country_id
        WHERE c.name LIKE ?
        GROUP BY c.id, c.name
        ORDER BY c.name
        LIMIT ? OFFSET ?
    ");
    $stmt->execute(["%$search%", $limit, $offset]);
    $countries = $stmt->fetchAll();
} catch(PDOException $e) {
    $_SESSION['error'] = 'حدث خطأ في جلب البيانات';
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الدول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/css/admin.css" rel="stylesheet">
</head>
<body>
    <div class="d-flex">
        <?php include 'sidebar.php'; ?>
        
        <div class="main-content p-4 w-100">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>إدارة الدول</h2>
                <div class="d-flex gap-2">
                    <form class="d-flex" method="GET">
                        <input type="text" class="form-control me-2" name="search" 
                               value="<?= htmlspecialchars($search) ?>" 
                               placeholder="بحث...">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i>
                        </button>
                    </form>
                    <a href="add_country.php" class="btn btn-primary">
                        <i class="bi bi-plus-lg me-1"></i> إضافة دولة
                    </a>
                </div>
            </div>

            <?php if(isset($_SESSION['success'])): ?>
                <div class="alert alert-success"><?= $_SESSION['success']; unset($_SESSION['success']); ?></div>
            <?php endif; ?>

            <?php if(isset($_SESSION['error'])): ?>
                <div class="alert alert-danger"><?= $_SESSION['error']; unset($_SESSION['error']); ?></div>
            <?php endif; ?>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم الدولة</th>
                                    <th>عدد المدن</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($countries as $country): ?>
                                <tr>
                                    <td><?= htmlspecialchars($country['id']) ?></td>
                                    <td><?= htmlspecialchars($country['name']) ?></td>
                                    <td><?= (int)$country['city_count'] ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" onclick="editCountry(<?= $country['id'] ?>)">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <form action="" method="POST" class="d-inline" 
                                              onsubmit="return confirm('هل أنت متأكد من حذف هذه الدولة؟')">
                                            <input type="hidden" name="country_id" value="<?= $country['id'] ?>">
                                            <button type="submit" name="delete_country" class="btn btn-sm btn-danger">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <?php if($totalPages > 1): ?>
                    <nav class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php for($i = 1; $i <= $totalPages; $i++): ?>
                                <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=<?= $i ?>"><?= $i ?></a>
                                </li>
                            <?php endfor; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
    function editCountry(countryId) {
        fetch('get_country.php?id=' + countryId)
            .then(response => response.json())
            .then(data => {
                document.getElementById('edit_country_id').value = data.id;
                document.getElementById('edit_country_name').value = data.name;
                new bootstrap.Modal(document.getElementById('editCountryModal')).show();
            })
            .catch(error => console.error('Error:', error));
    }

    document.querySelector('#addCountryModal form').addEventListener('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(this);
        
        fetch('countries.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(() => {
            window.location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إضافة الدولة');
        });
    });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
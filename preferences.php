<?php
// preferences.php
session_start();
require_once 'config.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $_SESSION['tour_data'] = $_POST;
}

// التحقق من وجود بيانات الرحلة
if (!isset($_SESSION['tour_data']['city'])) {
    header("Location: index.php");
    exit();
}

// الحصول على اسم المدينة من قاعدة البيانات
$cityName = 'غير محدد';
if (isset($_SESSION['tour_data']['city'])) {
    try {
        $stmt = $db->prepare("SELECT name FROM cities WHERE id = ?");
        $stmt->execute([$_SESSION['tour_data']['city']]);
        $city = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($city) {
            $cityName = $city['name'];
        }
    } catch (PDOException $e) {
        error_log("خطأ في جلب اسم المدينة: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>تفضيلات الرحلة المتقدمة</title>
       <!-- Bootstrap 5.3 CSS RTL -->
       <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f4f6f9;
        }
        .profile-container {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            padding: 30px;
            transition: all 0.3s ease;
        }
        .profile-container:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        .profile-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .profile-avatar {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            border: 5px solid #3498db;
        }
        .stats-card {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .stats-card:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .btn-edit-profile {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .spinner-container {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        .spinner-border {
            width: 3rem;
            height: 3rem;
        }
    </style>
</head>


<style>
    .navbar {
        margin-bottom: 20px;
        background-color: #f8f9fa !important;
    }
    .nav-link {
        transition: all 0.3s ease;
    }
    .nav-link:hover {
        color: #3498db !important;
        transform: translateY(-2px);
    }
    .dropdown-menu {
        min-width: 200px;
        left: auto !important;
        right: 0 !important;
        margin-top: 10px;
        transform: translateX(-10px);
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        border: none;
        border-radius: 10px;
    }
</style>
</head>
<?php include 'navbar.php'; ?>

<!-- CSS إضافي -->
<style>
    .navbar {
        margin-bottom: 20px;
        background-color: #f8f9fa !important;
    }
    .nav-link {
        transition: all 0.3s ease;
    }
    .nav-link:hover {
        color: #3498db !important;
        transform: translateY(-2px);
    }
    .dropdown-menu {
        min-width: 200px;
    }
</style>
<body>
    <div class="container my-5">
        <h2 class="text-center mb-4">تفضيلات الرحلة المتقدمة</h2>

        <?php
        // عرض رسائل الخطأ أو النجاح
        if (isset($_SESSION['flash_message'])) {
            $flash = $_SESSION['flash_message'];
            $alertClass = $flash['type'] == 'error' ? 'alert-danger' : 'alert-success';
            echo "<div class='alert {$alertClass} alert-dismissible fade show' role='alert'>";
            echo nl2br(htmlspecialchars($flash['message']));
            echo "<button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>";
            echo "</div>";
            unset($_SESSION['flash_message']);
        }
        ?>

        <form action="generate_plan.php" method="POST" class="max-w-800 mx-auto">
            <!-- حقول مخفية للبيانات الأساسية -->
            <input type="hidden" name="city" value="<?= htmlspecialchars($_SESSION['tour_data']['city'] ?? '') ?>">
            <input type="hidden" name="city_name" value="<?= htmlspecialchars($cityName) ?>">

            <!-- بيانات الرحلة الأساسية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title">معلومات الرحلة الأساسية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">المدينة:</label>
                            <input type="text" name="city_display" class="form-control"
                                   value="<?= htmlspecialchars($cityName) ?>"
                                   readonly style="background-color: #f8f9fa;">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">عدد أيام الرحلة:</label>
                            <select name="days" class="form-select" required>
                                <?php for($i=1; $i<=14; $i++) { ?>
                                    <option value="<?php echo $i; ?>"><?php echo $i; ?> يوم</option>
                                <?php } ?>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">عدد المسافرين:</label>
                            <select name="travelers_count" class="form-select" required>
                                <?php for($i=1; $i<=10; $i++) { ?>
                                    <option value="<?php echo $i; ?>"><?php echo $i; ?> أشخاص</option>
                                <?php } ?>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">نوع الرحلة:</label>
                            <select name="trip_type" class="form-select">
                                <option value="عائلية">رحلة عائلية</option>
                                <option value="أصدقاء">رحلة أصدقاء</option>
                                <option value="فردية">رحلة فردية</option>
                                <option value="زوجية">رحلة زوجية</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أنواع السكن -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title">خيارات السكن</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">نوع السكن:</label>
                            <div class="form-check">
                                <input type="checkbox" name="accommodation[]" value="فندق 5 نجوم" class="form-check-input">
                                <label class="form-check-label">فندق 5 نجوم</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="accommodation[]" value="فندق 4 نجوم" class="form-check-input">
                                <label class="form-check-label">فندق 4 نجوم</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="accommodation[]" value="شقة فندقية" class="form-check-input">
                                <label class="form-check-label">شقة فندقية</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="accommodation[]" value="منتجع" class="form-check-input">
                                <label class="form-check-label">منتجع سياحي</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">إطلالة السكن:</label>
                            <div class="form-check">
                                <input type="checkbox" name="accommodation_view[]" value="بحر" class="form-check-input">
                                <label class="form-check-label">إطلالة بحرية</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="accommodation_view[]" value="مدينة" class="form-check-input">
                                <label class="form-check-label">إطلالة على المدينة</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="accommodation_view[]" value="جبل" class="form-check-input">
                                <label class="form-check-label">إطلالة جبلية</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الوجبات والطعام -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title">تفضيلات الطعام</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">أنواع المطاعم:</label>
                            <div class="form-check">
                                <input type="checkbox" name="restaurants[]" value="مطاعم محلية" class="form-check-input">
                                <label class="form-check-label">مطاعم محلية</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="restaurants[]" value="مطاعم عالمية" class="form-check-input">
                                <label class="form-check-label">مطاعم عالمية</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="restaurants[]" value="مطاعم عربية" class="form-check-input">
                                <label class="form-check-label">مطاعم عربية</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">نوع المأكولات:</label>
                            <div class="form-check">
                                <input type="checkbox" name="food_type[]" value="نباتي" class="form-check-input">
                                <label class="form-check-label">طعام نباتي</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="food_type[]" value="لحوم" class="form-check-input">
                                <label class="form-check-label">لحوم حلال</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="food_type[]" value="بحري" class="form-check-input">
                                <label class="form-check-label">مأكولات بحرية</label>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="row">
                        <div class="col-md-12">
                            <label class="form-label">الوجبات المفضلة:</label>
                            <div class="d-flex justify-content-between">
                                <div class="form-check">
                                    <input type="checkbox" name="meals[]" value="فطور" class="form-check-input">
                                    <label class="form-check-label">فطور</label>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" name="meals[]" value="فطور متأخر" class="form-check-input">
                                    <label class="form-check-label">فطور متأخر</label>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" name="meals[]" value="غداء" class="form-check-input">
                                    <label class="form-check-label">غداء</label>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" name="meals[]" value="عشاء" class="form-check-input">
                                    <label class="form-check-label">عشاء</label>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" name="meals[]" value="سناكس" class="form-check-input">
                                    <label class="form-check-label">سناكس</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الأنشطة والترفيه -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title">الأنشطة والترفيه</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">أنشطة سياحية:</label>
                            <div class="form-check">
                                <input type="checkbox" name="activities[]" value="تسوق" class="form-check-input">
                                <label class="form-check-label">تسوق</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="activities[]" value="معالم سياحية" class="form-check-input">
                                <label class="form-check-label">معالم سياحية</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="activities[]" value="مغامرات" class="form-check-input">
                                <label class="form-check-label">مغامرات</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="activities[]" value="ترفيه" class="form-check-input">
                                <label class="form-check-label">ترفيه</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">أماكن ترفيهية:</label>
                            <div class="form-check">
                                <input type="checkbox" name="entertainment[]" value="كوفيهات" class="form-check-input">
                                <label class="form-check-label">كوفيهات</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="entertainment[]" value="مولات" class="form-check-input">
                                <label class="form-check-label">مولات تسوق</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="entertainment[]" value="مسارح" class="form-check-input">
                                <label class="form-check-label">مسارح</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="entertainment[]" value="متاحف" class="form-check-input">
                                <label class="form-check-label">متاحف</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="entertainment[]" value="حدائق" class="form-check-input">
                                <label class="form-check-label">حدائق وملاهي</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- وسائل التنقل -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title">وسائل التنقل</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">وسائل النقل:</label>
                            <div class="form-check">
                                <input type="checkbox" name="transport[]" value="تاكسي" class="form-check-input">
                                <label class="form-check-label">تاكسي</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="transport[]" value="أوبر" class="form-check-input">
                                <label class="form-check-label">أوبر</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="transport[]" value="سيارة مستأجرة" class="form-check-input">
                                <label class="form-check-label">سيارة مستأجرة</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">نوع السيارة:</label>
                            <select name="car_type" class="form-select">
                                <option value="اقتصادية">سيارة اقتصادية</option>
                                <option value="فاخرة">سيارة فاخرة</option>
                                <option value="عائلية">سيارة عائلية</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ميزانية الرحلة -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title">الميزانية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">الميزانية التقديرية:</label>
                            <select name="budget" class="form-select">
                            <option value="منخفضة">ميزانية منخفضة (أقل من 1000 ريال)</option>
                                <option value="متوسطة">ميزانية متوسطة (1000-3000 ريال)</option>
                                <option value="مرتفعة">ميزانية مرتفعة (3000-7000 ريال)</option>
                                <option value="فاخرة">ميزانية فاخرة (أكثر من 7000 ريال)</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تفضيلات الإنفاق:</label>
                            <div class="form-check">
                                <input type="checkbox" name="spending_preferences[]" value="توفير" class="form-check-input">
                                <label class="form-check-label">التوفير قدر الإمكان</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="spending_preferences[]" value="متوازن" class="form-check-input">
                                <label class="form-check-label">إنفاق متوازن</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="spending_preferences[]" value="فاخر" class="form-check-input">
                                <label class="form-check-label">إنفاق فاخر</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- متطلبات خاصة -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title">متطلبات خاصة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">احتياجات خاصة:</label>
                            <div class="form-check">
                                <input type="checkbox" name="special_needs[]" value="نباتي" class="form-check-input">
                                <label class="form-check-label">طعام نباتي</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="special_needs[]" value="حلال" class="form-check-input">
                                <label class="form-check-label">طعام حلال</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" name="special_needs[]" value="معاق" class="form-check-input">
                                <label class="form-check-label">متطلبات ذوي الإعاقة</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">ملاحظات إضافية:</label>
                            <textarea name="additional_notes" class="form-control" rows="3" placeholder="أي متطلبات أو تفضيلات إضافية"></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- زر إنشاء البرنامج -->
            <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="bi bi-magic me-2"></i> إنشاء البرنامج السياحي
                </button>
                <div class="mt-3">
                    <a href="test_api.php" class="btn btn-outline-secondary btn-sm me-2">
                        <i class="bi bi-gear me-1"></i> اختبار API
                    </a>
                    <a href="debug_api.php" class="btn btn-outline-info btn-sm">
                        <i class="bi bi-bug me-1"></i> تشخيص مفصل
                    </a>
                </div>
            </div>
        </form>
    </div>
  <!-- Bootstrap JS (اختياري) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- رابط أيقونات Bootstrap (اختياري) -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
   
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // التحقق من اختيار على الأقل خيار واحد في كل مجموعة
        const form = document.querySelector('form');
        form.addEventListener('submit', function(event) {
            const requiredSections = [
                'accommodation', 
                'restaurants', 
                'activities', 
                'meals'
            ];

            for (let section of requiredSections) {
                const checkboxes = document.querySelectorAll(`input[name="${section}[]"]`);
                const isChecked = Array.from(checkboxes).some(checkbox => checkbox.checked);

                if (!isChecked) {
                    event.preventDefault();
                    alert(`يرجى اختيار خيار واحد على الأقل في قسم ${section}`);
                    return;
                }
            }

            // Show loading spinner if validation passes
            const spinner = document.getElementById('loadingSpinner');
            if (spinner) {
                spinner.style.display = 'flex';
            }

            // تعطيل زر الإرسال لمنع الإرسال المتكرر
            const submitBtn = event.target.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i> جاري المعالجة...';
            }

            return true;
        });
    });
    </script>

    <!-- Add spinner HTML before closing body tag -->
    <div class="loading-overlay" id="loadingSpinner" style="display: none;">
        <div class="spinner-container">
            <div class="spinner-border text-primary" role="status"></div>
            <div class="mt-2">جاري إعداد البرنامج...</div>
        </div>
    </div>

</body>
</html>
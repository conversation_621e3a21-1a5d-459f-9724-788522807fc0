<?php
header('Content-Type: application/json');
require_once 'config.php';

function callGoogleAI($prompt) {
    $apiKey = 'AIzaSyA7G2VbfTCoh392tSwmD8lNOPRwqJFBuCw';
    $url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=' . $apiKey;
    
    $data = [
        'contents' => [
            ['parts' => [['text' => $prompt]]]
        ]
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);

    $response = curl_exec($ch);
    curl_close($ch);
    return json_decode($response, true);
}

$data = json_decode(file_get_contents('php://input'), true);
$city = $data['city'] ?? '';

if (empty($city)) {
    echo json_encode(['error' => 'يرجى اختيار المدينة']);
    exit;
}

$prompt = "اقترح برنامج سياحي ليوم واحد في مدينة {$city} يتضمن:
- فندق 5 أو 4 نجوم للإقامة
- برنامج من الساعة 10 صباحاً حتى 10 مساءً
- أماكن الإفطار المميزة وأسباب اختيارها
- أنشطة تسوق ونشاطات متنوعة
- مطاعم للغداء مع تقييمات عالية
- كافيهات لفترة ما قبل الغروب
- أنشطة ليلية
- مطعم فاخر للعشاء
- معلومات عن أوقات الفتح والإغلاق والازدحام
- اظهر مواقع  او معلومات روابط على النت  بحيث يكون الرابط كالتالي ( اسم المكان المقترح+اسم المدينة {$city} ويتم ارسالها لمحرك البحث قوقل وجلب البيانات او الرابط الخاصة بالمكان
- وسائل التنقل المتاحة";


$response = callGoogleAI($prompt);
echo json_encode(['suggestion' => $response]);
?>
<?php
session_start();
require_once '../config.php';
require_once 'auth_check.php';

// Get statistics
$stats = [
    'users' => $db->query("SELECT COUNT(*) FROM users")->fetchColumn(),
    'tours' => $db->query("SELECT COUNT(*) FROM tour_plans")->fetchColumn(),
    'cities' => $db->query("SELECT COUNT(*) FROM cities")->fetchColumn(),
    'countries' => $db->query("SELECT COUNT(*) FROM countries")->fetchColumn()
];
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/css/admin.css" rel="stylesheet">
</head>
<body>
<?php include 'sidebar.php'; ?>

        <!-- Main Content -->
        <div class="main-content p-4 w-80">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>لوحة التحكم</h2>
                <div class="user-info">
                    <span class="me-2"><?= htmlspecialchars($_SESSION['admin_username']) ?></span>
                    <i class="bi bi-person-circle"></i>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row">
                <div class="col-md-2">
                    <div class="card bg-primary text-white mb-4">
                        <div class="card-body">
                            <h5><i class="bi bi-people me-2"></i>المستخدمين</h5>
                            <h2 class="mb-0"><?= $stats['users'] ?></h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-success text-white mb-4">
                        <div class="card-body">
                            <h5><i class="bi bi-compass me-2"></i>البرامج السياحية</h5>
                            <h2 class="mb-0"><?= $stats['tours'] ?></h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-info text-white mb-4">
                        <div class="card-body">
                            <h5><i class="bi bi-geo-alt me-2"></i>المدن</h5>
                            <h2 class="mb-0"><?= $stats['cities'] ?></h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-info text-white mb-4">
                        <div class="card-body">
                            <h5><i class="bi bi-geo-alt me-2"></i>المدن</h5>
                            <h2 class="mb-0"><?= $stats['cities'] ?></h2>
                        </div>
                    </div>
                </div>
            
                <div class="col-md-2">
                    <div class="card bg-info text-white mb-4">
                        <div class="card-body">
                            <h5><i class="bi bi-geo-alt me-2"></i>الدول</h5>
                            <h2 class="mb-0"><?= $stats['countries'] ?></h2>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <!-- <div class="card mt-4">
                <div class="card-header">
                    <h5 >آخر النشاطات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <!-- Add recent activities table -->
                        </table>
                    </div>
                </div>
            </div> -->
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
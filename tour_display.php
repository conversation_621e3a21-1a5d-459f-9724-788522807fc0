<?php
// تضمين ملفات الإعداد والجلسة
session_start();
require_once 'config.php';
require_once 'session_handler.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من وجود معرف الرحلة
$tourId = $_GET['id'] ?? null;
if (!$tourId) {
    die('لم يتم تحديد معرف الرحلة');
}

// استرجاع بيانات الرحلة من قاعدة البيانات
try {
    $stmt = $db->prepare("SELECT * FROM tour_plans WHERE id = ? AND user_id = ?");
    $stmt->execute([$tourId, $_SESSION['user_id']]);
    $tour = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$tour) {
        die('لم يتم العثور على الرحلة المطلوبة');
    }
} catch (PDOException $e) {
    die('خطأ في قاعدة البيانات: ' . $e->getMessage());
}
function cleanMapLinks($text) {
    // إزالة الروابط الكاملة واستبدالها بأيقونات فقط
    $text = preg_replace_callback(
        '/<a\s+href="([^"]+)"\s+target="_blank"><i\s+class="[^"]*fa-(?:location-dot|solid)[^"]*"><\/i><\/a>/',
        function($matches) {
            return '<a href="' . $matches[1] . '" class="location-icon-link" target="_blank" title="عرض على الخريطة"><i class="fas fa-map-marker-alt"></i></a>';
        },
        $text
    );

    // إزالة أي روابط خرائط نصية وتحويلها لأيقونات
    $text = preg_replace_callback(
        '/https?:\/\/(?:www\.)?google\.com\/maps\/[^\s<>\)]+/',
        function($matches) {
            return '<a href="' . $matches[0] . '" class="location-icon-link" target="_blank" title="عرض على الخريطة"><i class="fas fa-map-marker-alt"></i></a>';
        },
        $text
    );

    // إزالة أي روابط مكررة متتالية
    $text = preg_replace(
        '/(<a[^>]*class="location-icon-link"[^>]*>.*?<\/a>)\s*\1+/',
        '$1',
        $text
    );

    // تنظيف المسافات الزائدة حول الأيقونات
    $text = preg_replace('/\s+(<a[^>]*class="location-icon-link"[^>]*>.*?<\/a>)\s+/', ' $1 ', $text);

    return $text;
}

function formatTourContent($content) {
    if (empty($content)) {
        return '<div class="alert alert-warning">لا يوجد محتوى متاح للعرض</div>';
    }

    // تنظيف الروابط أولاً
    $content = cleanMapLinks($content);

    $lines = explode("\n", $content);
    $formattedContent = '';
    $currentDay = '';
    $inCostSection = false;

    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;

        // تنسيق العناوين الرئيسية (##)
        if (preg_match('/^##\s*(.+)/', $line, $matches)) {
            $title = trim($matches[1]);
            $formattedContent .= sprintf(
                '<div class="day-header">
                    <h2 class="day-title">
                        <i class="fas fa-calendar-day"></i> %s
                    </h2>
                </div>',
                htmlspecialchars($title)
            );
            continue;
        }

        // تنسيق العناوين الفرعية (#)
        if (preg_match('/^#\s*(.+)/', $line, $matches)) {
            $subtitle = trim($matches[1]);
            $formattedContent .= sprintf(
                '<div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-map-marker-alt"></i> %s
                    </h3>
                </div>',
                htmlspecialchars($subtitle)
            );
            continue;
        }

        // تنسيق الأيام (اليوم الأول، اليوم الثاني، إلخ)
        if (preg_match('/^(اليوم\s+.+)/', $line, $matches)) {
            $dayTitle = trim($matches[1]);
            $formattedContent .= sprintf(
                '<div class="day-section">
                    <h4 class="day-number">
                        <i class="fas fa-sun"></i> %s
                    </h4>
                </div>',
                htmlspecialchars($dayTitle)
            );
            continue;
        }

        // تنسيق الأوقات والأنشطة
        if (preg_match('/(\d{1,2}:\d{2}\s*(?:صباحًا|مساءً|ظهرًا|AM|PM))\s*[-:]?\s*(.+)/', $line, $matches)) {
            $time = trim($matches[1]);
            $activity = trim($matches[2]);

            // إضافة روابط الخرائط
            $activity = addMapLinks($activity);

            $formattedContent .= sprintf(
                '<div class="timeline-item">
                    <div class="timeline-badge">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-time">%s</div>
                        <div class="timeline-activity">
                            <span class="activity-text">%s</span>
                        </div>
                    </div>
                </div>',
                htmlspecialchars($time),
                $activity
            );
            continue;
        }

        // تنسيق أقسام التكلفة
        if (preg_match('/^(التكلفة|الميزانية|إجمالي|المجموع)/i', $line)) {
            $inCostSection = true;
            $formattedContent .= '<div class="cost-section">';
        }

        // تنسيق النقاط والقوائم
        if (preg_match('/^[-*•]\s*(.+)/', $line, $matches)) {
            $item = trim($matches[1]);
            $item = addMapLinks($item);
            $formattedContent .= sprintf(
                '<div class="list-item">
                    <i class="fas fa-check-circle"></i>
                    <div class="content">%s</div>
                </div>',
                $item
            );
            continue;
        }

        // تنسيق النصوص المميزة (بين نجمتين)
        if (preg_match('/^\*\*(.+)\*\*$/', $line, $matches)) {
            $highlighted = trim($matches[1]);
            $highlighted = addMapLinks($highlighted);
            $formattedContent .= sprintf(
                '<div class="highlighted-text">
                    <i class="fas fa-star"></i> %s
                </div>',
                $highlighted
            );
            continue;
        }

        // تنسيق الملاحظات (تبدأ بـ "ملاحظة" أو "تنبيه")
        if (preg_match('/^(ملاحظة|تنبيه|مهم):\s*(.+)/i', $line, $matches)) {
            $noteType = trim($matches[1]);
            $noteText = trim($matches[2]);
            $noteText = addMapLinks($noteText);
            $formattedContent .= sprintf(
                '<div class="note-item">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>%s:</strong> %s
                </div>',
                $noteType,
                $noteText
            );
            continue;
        }

        // تنسيق النص العادي
        if (!empty($line)) {
            $line = addMapLinks($line);
            // التحقق من وجود أرقام (قد تكون أسعار)
            if (preg_match('/\d+\s*(ريال|درهم|ليرة|دولار|يورو)/', $line)) {
                $formattedContent .= sprintf(
                    '<div class="price-text">
                        <i class="fas fa-money-bill-wave"></i> %s
                    </div>',
                    htmlspecialchars($line)
                );
            } else {
                $formattedContent .= sprintf(
                    '<div class="regular-text">%s</div>',
                    nl2br(htmlspecialchars($line))
                );
            }
        }
    }

    if ($inCostSection) {
        $formattedContent .= '</div>';
    }

    return $formattedContent;
}

function addMapLinks($text) {
    // تنظيف أي روابط موجودة مسبقاً
    $text = cleanMapLinks($text);

    // قائمة أماكن شائعة مع روابط الخرائط
    $locations = [
        // أماكن تركية
        'آيا صوفيا' => 'Hagia+Sophia+Istanbul',
        'البازار الكبير' => 'Grand+Bazaar+Istanbul',
        'برج غلطة' => 'Galata+Tower+Istanbul',
        'مضيق البوسفور' => 'Bosphorus+Istanbul',
        'قصر توب كابي' => 'Topkapi+Palace+Istanbul',
        'المسجد الأزرق' => 'Blue+Mosque+Istanbul',
        'قصر دولما بهجة' => 'Dolmabahce+Palace+Istanbul',
        'جسر البوسفور' => 'Bosphorus+Bridge+Istanbul',
        'تقسيم' => 'Taksim+Square+Istanbul',
        'أورتاكوي' => 'Ortakoy+Istanbul',
        'كاباتاش' => 'Kabatas+Istanbul',
        'أمينونو' => 'Eminonu+Istanbul',
        'بشيكتاش' => 'Besiktas+Istanbul',
        'كاديكوي' => 'Kadikoy+Istanbul',
        'فاتح' => 'Fatih+Istanbul',
        'سلطان أحمد' => 'Sultanahmet+Istanbul',
        'بيوغلو' => 'Beyoglu+Istanbul',
        'شيشلي' => 'Sisli+Istanbul',

        // أماكن إماراتية
        'برج خليفة' => 'Burj+Khalifa+Dubai',
        'دبي مول' => 'Dubai+Mall',
        'نافورة دبي' => 'Dubai+Fountain',
        'برج العرب' => 'Burj+Al+Arab+Dubai',
        'جزيرة النخلة' => 'Palm+Jumeirah+Dubai',
        'مارينا دبي' => 'Dubai+Marina',
        'سوق الذهب' => 'Gold+Souk+Dubai',

        // أماكن سعودية
        'الحرم المكي' => 'Masjid+al+Haram+Mecca',
        'المسجد النبوي' => 'Prophet+Mosque+Medina',
        'برج المملكة' => 'Kingdom+Tower+Riyadh',

        // أماكن ماليزية
        'أبراج بتروناس' => 'Petronas+Towers+Kuala+Lumpur',

        // أماكن سنغافورية
        'مارينا باي' => 'Marina+Bay+Singapore'
    ];

    foreach ($locations as $place => $mapQuery) {
        if (stripos($text, $place) !== false) {
            $mapUrl = "https://www.google.com/maps/search/" . urlencode($mapQuery);
            $text = str_ireplace(
                $place,
                sprintf(
                    '%s <a href="%s" class="location-icon-link" target="_blank" title="عرض %s على الخريطة"><i class="fas fa-map-marker-alt"></i></a>',
                    $place,
                    $mapUrl,
                    $place
                ),
                $text
            );
        }
    }

    return $text;
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الرحلة - <?= htmlspecialchars($tour['city']) ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@500;700&display=swap">
    <style>
/* التصميم الأساسي */
.tour-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: #fff;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    border-radius: 10px;
}

/* تنسيق الجدول الزمني */
.timeline-item {
    position: relative;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-right: 4px solid #3498db;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.timeline-badge {
    position: absolute;
    right: -12px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background: #3498db;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.timeline-badge i {
    color: white;
    font-size: 12px;
}

.timeline-time {
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.1em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.timeline-time i {
    color: #3498db;
    margin-left: 5px;
}

.timeline-content {
    font-size: 1.1em;
    line-height: 1.6;
}

/* تنسيق العناوين */
.day-title {
    color: #2c3e50;
    font-size: 1.8em;
    margin: 30px 0 20px;
    padding: 15px;
    background: rgba(52, 152, 219, 0.1);
    border-radius: 8px;
    border-right: 4px solid #3498db;
    display: flex;
    align-items: center;
    gap: 10px;
}

.day-icon {
    color: #3498db;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #2c3e50;
    font-size: 1.5em;
    margin: 25px 0 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3498db;
}

.section-icon {
    color: #3498db;
}

/* تنسيق الروابط والمواقع */
.location-link {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    color: #3498db;
    text-decoration: none;
    background: rgba(52, 152, 219, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
    margin: 0 5px;
}

.location-link:hover {
    background: rgba(52, 152, 219, 0.2);
    transform: translateY(-2px);
}

.location-icon {
    color: #e74c3c;
}

.location-link i {
    color: #e74c3c;
    margin-right: 5px;
}

/* تنسيق قسم الملاحظات */
.notes-section {
    background: #f7f9fc;
    padding: 25px;
    border-radius: 15px;
    margin: 30px 0;
    border-right: 5px solid #3498db;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.notes-title {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #2c3e50;
    font-size: 1.5em;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3498db;
}

.notes-content {
    font-size: 1.1em;
    line-height: 1.6;
}

/* تنسيق النص العادي */
.regular-text {
    font-size: 1.1em;
    line-height: 1.7;
    color: #2c3e50;
    margin: 10px 0;
    padding: 8px 0;
}

/* تنسيق العناوين الجديدة */
.day-header {
    margin: 30px 0 20px;
}

.day-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    font-size: 1.8em;
    font-weight: bold;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin: 0;
}

.day-title i {
    margin-left: 10px;
    font-size: 1.2em;
}

.section-header {
    margin: 25px 0 15px;
}

.section-title {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-right: 4px solid #28a745;
    color: #2c3e50;
    font-size: 1.4em;
    font-weight: 600;
    margin: 0;
}

.section-title i {
    color: #28a745;
    margin-left: 8px;
}

.day-section {
    margin: 20px 0;
}

.day-number {
    background: linear-gradient(45deg, #ff6b6b, #ffa500);
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    font-size: 1.3em;
    font-weight: 600;
    text-align: center;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    margin: 0;
}

.day-number i {
    margin-left: 8px;
}

/* تحسين Timeline */
.timeline-item {
    position: relative;
    margin-bottom: 25px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    border-right: 4px solid #3498db;
    box-shadow: 0 3px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.timeline-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 25px rgba(0,0,0,0.12);
}

.timeline-badge {
    position: absolute;
    right: -12px;
    top: 20px;
    width: 24px;
    height: 24px;
    background: #3498db;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.timeline-badge i {
    color: white;
    font-size: 10px;
}

.timeline-time {
    font-weight: bold;
    color: #3498db;
    font-size: 1.1em;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.timeline-activity {
    font-size: 1.05em;
    line-height: 1.6;
    color: #2c3e50;
}

/* تنسيق القوائم */
.list-item {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    margin: 12px 0;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border-right: 3px solid #28a745;
}

.list-item i {
    color: #28a745;
    margin-top: 2px;
    flex-shrink: 0;
}

/* تنسيق قسم التكلفة */
.cost-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 15px;
    margin: 25px 0;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.cost-section .regular-text {
    color: white;
    font-weight: 500;
}

.cost-section .list-item {
    background: rgba(255,255,255,0.1);
    border-right: 3px solid #ffd700;
    color: white;
}

.cost-section .list-item i {
    color: #ffd700;
}

/* تنسيق المحتوى الخام */
.raw-content {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-right: 4px solid #17a2b8;
    margin-top: 15px;
    font-family: 'Courier New', monospace;
    font-size: 0.95em;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 400px;
    overflow-y: auto;
}

/* تحسين التنبيهات */
.alert {
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
    border: none;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.alert-info {
    background: linear-gradient(135deg, #17a2b8, #20c997);
    color: white;
}

.alert-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
}

.alert i {
    margin-left: 8px;
    font-size: 1.1em;
}

/* تنسيق النصوص المميزة */
.highlighted-text {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #2c3e50;
    padding: 15px;
    border-radius: 10px;
    margin: 15px 0;
    font-weight: 600;
    box-shadow: 0 3px 10px rgba(255, 215, 0, 0.3);
    border-right: 4px solid #f39c12;
}

.highlighted-text i {
    color: #f39c12;
    margin-left: 8px;
}

/* تنسيق الملاحظات */
.note-item {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-right: 4px solid #fdcb6e;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    color: #856404;
}

.note-item i {
    color: #e17055;
    margin-left: 8px;
}

.note-item strong {
    color: #d63031;
}

/* تنسيق النصوص التي تحتوي على أسعار */
.price-text {
    background: linear-gradient(135deg, #00b894, #00cec9);
    color: white;
    padding: 12px 15px;
    border-radius: 8px;
    margin: 10px 0;
    font-weight: 500;
    box-shadow: 0 3px 10px rgba(0, 184, 148, 0.3);
}

.price-text i {
    color: #ffd700;
    margin-left: 8px;
}

/* تحسين عام للمحتوى */
.tour-content {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    margin: 20px 0;
}

/* تحسين الروابط */
.location-link {
    color: #e74c3c !important;
    text-decoration: none;
    padding: 2px 6px;
    border-radius: 4px;
    background: rgba(231, 76, 60, 0.1);
    transition: all 0.3s ease;
    margin: 0 3px;
}

.location-link:hover {
    background: rgba(231, 76, 60, 0.2);
    transform: scale(1.05);
}

.location-link i {
    color: #e74c3c;
    margin-right: 3px;
}

/* تنسيق أيقونات الموقع الجديدة */
.location-icon-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white !important;
    border-radius: 50%;
    text-decoration: none;
    margin: 0 5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
    font-size: 12px;
}

.location-icon-link:hover {
    transform: scale(1.1) translateY(-2px);
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
    background: linear-gradient(135deg, #c0392b, #a93226);
}

.location-icon-link i {
    color: white !important;
    margin: 0;
    font-size: 12px;
}

/* تحسين عرض النص مع الأيقونات */
.timeline-activity {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 8px;
    line-height: 1.6;
}

.activity-text {
    flex: 1;
    min-width: 0;
}

.timeline-activity .location-icon-link,
.regular-text .location-icon-link,
.list-item .location-icon-link {
    flex-shrink: 0;
    vertical-align: middle;
}

.regular-text {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 5px;
}

.list-item {
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.list-item > i {
    margin-top: 2px;
    flex-shrink: 0;
}

.list-item .content {
    flex: 1;
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 5px;
}

/* تحسينات للطباعة */
@media print {
    .tour-container {
        box-shadow: none;
        margin: 0;
        padding: 0;
    }
    
    .timeline-item {
        page-break-inside: avoid;
        border: 1px solid #ddd;
    }
    
    .location-link {
        text-decoration: underline;
        background: none;
    }
    
    .no-print {
        display: none !important;
    }
}
</style>
<style>
.arabic-content {
    direction: rtl;
    text-align: right;
    font-family: 'Tajawal', sans-serif;
    line-height: 1.5;
    font-size: 1.1em;
    font-weight: 500;
    padding: 20px;
   
}


        .tour-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            direction: rtl;
            font-family: 'Tajawal', sans-serif;
        }
        
        .tour-header {
            background-color: #f8f9fa;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        

    .location-link {
        display: inline-flex;
        align-items: center;
        font-size: 1.1em;
        color: #3498db;
        text-decoration: none;
        transition: all 0.3s;
        padding: 8px 15px;
        border-radius: 4px;
        background-color: #f8f9fa;
        margin: 0 5px;
    }

    .location-link i {
        color:rgb(224, 37, 16);  /* Red color for location icon */
        font-size: 1.2em;
        margin-left: 8px;
    }

    .timeline-content {
        background: #fff;
        padding: 15px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        font-size: 1.1em;  /* Increased font size */
        line-height: 1.6;
    }

    .timeline-time {
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 5px;
        font-size: 1.15em;  /* Increased time font size */
    }

    .tour-text {
        font-size: 1.1em;
        line-height: 1.7;
    }

        .tour-title {
            margin: 0;
            color: #2c3e50;
            font-size: 2em;
            margin-bottom: 10px;
        }

        .tour-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .meta-item {
            padding: 10px;
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .meta-label {
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }

        .timeline {
            list-style: none;
            padding: 0;
            position: relative;
        }
        
        .timeline:before {
            content: '';
            position: absolute;
            top: 0;
            right: 20px;
            height: 100%;
            width: 2px;
            background: #e9ecef;
        }
        
        .timeline-item {
            margin-bottom: 20px;
            position: relative;
            padding-right: 50px;
        }
        
        .timeline-item:before {
            content: '';
            position: absolute;
            right: 16px;
            top: 0;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #3498db;
            border: 2px solid #fff;
        }
        
        .timeline-time {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .timeline-content {
            background: #fff;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .tour-day-title {
            background-color: #f8f9fa;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            color: #2c3e50;
            font-size: 1.5em;
        }

        .location-link {
            display: inline-block;
            margin-right: 10px;
            color: #3498db;
            text-decoration: none;
            transition: all 0.3s;
            padding: 5px 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        
        .location-link:hover {
            color: #2980b9;
            background-color: #e9ecef;
            transform: translateY(-2px);
        }

        .notes-section {
            background: #f7f9fc;
            padding: 25px;
            border-radius: 15px;
            margin: 30px 0;
            border-right: 5px solid #3498db;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .notes-section h3 {
            color: #2c3e50;
            font-size: 1.5em;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .notes-content {
            color: #34495e;
            line-height: 1.8;
            font-size: 1.1em;
            padding: 10px;
        }

        .notes-section ul {
            padding-right: 20px;
            margin: 10px 0;
        }

        .notes-section li {
            margin-bottom: 10px;
            position: relative;
        }

        .notes-section li:before {
            content: '•';
            color: #3498db;
            font-weight: bold;
            margin-left: 10px;
        }

        .cost-section {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.3em;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }

        .additional-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .detail-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .detail-label {
            font-weight: bold;
            color: #3498db;
            margin-bottom: 8px;
            font-size: 1.1em;
        }

        .btn-primary {
            background-color: #3498db;
            border: none;
            padding: 8px 15px;
            transition: all 0.3s;
        }

        .btn-primary:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }

        .program-title {
            text-align: center;
            font-size: 2.2em;
            color: white;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 30px;
            margin: 30px auto;
            border-radius: 15px;
            font-weight: bold;
            max-width: 900px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            position: relative;
            overflow: hidden;
        }

        .program-title::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="60" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
            pointer-events: none;
        }

        .tour-header {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .tour-title {
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5em;
            font-weight: 800;
            text-align: center;
            margin-bottom: 20px;
        }

        /* تنسيق بطاقات المعلومات */
        .info-cards-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .info-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .info-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .info-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5em;
            flex-shrink: 0;
        }

        .info-content {
            flex: 1;
        }

        .info-label {
            font-size: 0.9em;
            color: #6c757d;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .info-value {
            font-size: 1.3em;
            font-weight: 700;
            color: #2c3e50;
        }

        /* تنسيق قسم التفاصيل */
        .details-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            box-shadow: 0 3px 15px rgba(0,0,0,0.05);
        }

        .details-title {
            color: #2c3e50;
            font-size: 1.6em;
            font-weight: 700;
            margin-bottom: 25px;
            text-align: center;
            padding-bottom: 15px;
            border-bottom: 3px solid #3498db;
        }

        .details-title i {
            color: #3498db;
            margin-left: 10px;
        }

        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .detail-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.08);
            border-top: 4px solid #3498db;
            transition: all 0.3s ease;
        }

        .detail-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.12);
        }

        .detail-header {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 12px;
            font-size: 1.1em;
        }

        .detail-header i {
            color: #3498db;
            font-size: 1.2em;
        }

        .detail-content {
            color: #555;
            line-height: 1.6;
            font-size: 1.05em;
        }
    </style>

</head>
<body>
    <div class="tour-container">
        <div class="tour-header">
            <h1 class="tour-title">تفاصيل رحلة <?= htmlspecialchars($tour['city']) ?></h1>
            <?php if(!empty($tour['map_url'])): ?>
                <a href="<?= htmlspecialchars($tour['map_url']) ?>" target="_blank" class="btn btn-primary mb-3">
                    <i class="fas fa-map-marker-alt"></i> عرض على الخريطة
                </a>
            <?php endif; ?>

            <!-- معلومات الرحلة الأساسية -->
            <div class="info-cards-container">
                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="info-content">
                        <div class="info-label">الوجهة</div>
                        <div class="info-value"><?= htmlspecialchars($tour['city']) ?></div>
                    </div>
                </div>

                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="info-content">
                        <div class="info-label">مدة الرحلة</div>
                        <div class="info-value"><?= $tour['days_count'] ?> أيام</div>
                    </div>
                </div>

                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="info-content">
                        <div class="info-label">عدد المسافرين</div>
                        <div class="info-value"><?= $tour['travelers_count'] ?> أشخاص</div>
                    </div>
                </div>

                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="info-content">
                        <div class="info-label">الميزانية</div>
                        <div class="info-value"><?= number_format($tour['total_budget'], 0) ?> ريال</div>
                    </div>
                </div>
            </div>

            <!-- تفاصيل إضافية -->
            <div class="details-section">
                <h3 class="details-title">
                    <i class="fas fa-info-circle"></i> تفاصيل الرحلة
                </h3>

                <div class="details-grid">
                    <div class="detail-card">
                        <div class="detail-header">
                            <i class="fas fa-bed"></i>
                            <span>الإقامة</span>
                        </div>
                        <div class="detail-content">
                            <?= htmlspecialchars($tour['accommodation']) ?>
                        </div>
                    </div>

                    <div class="detail-card">
                        <div class="detail-header">
                            <i class="fas fa-utensils"></i>
                            <span>المطاعم</span>
                        </div>
                        <div class="detail-content">
                            <?= htmlspecialchars($tour['restaurants']) ?>
                        </div>
                    </div>

                    <div class="detail-card">
                        <div class="detail-header">
                            <i class="fas fa-hiking"></i>
                            <span>الأنشطة</span>
                        </div>
                        <div class="detail-content">
                            <?= htmlspecialchars($tour['activities']) ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="program-title">
            برنامج سياحي لمدة <?= $tour['days_count'] ?> أيام في <?= htmlspecialchars($tour['city']) ?>
        </div>

        <div class="tour-content">
            <?php
            $formattedContent = formatTourContent($tour['plan_details']);
            if (empty(trim(strip_tags($formattedContent)))) {
                // إذا لم يتم تنسيق المحتوى، عرض المحتوى الخام مع تنظيف الروابط
                $rawContent = cleanMapLinks($tour['plan_details']);
                $rawContent = addMapLinks($rawContent);
                echo '<div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>محتوى الرحلة:</strong>
                        <div class="raw-content">' . nl2br($rawContent) . '</div>
                      </div>';
            } else {
                echo $formattedContent;
            }
            ?>
        </div>

        <?php if(!empty($tour['notes'])): ?>
        <div class="notes-section">
            <h4 class="notes-title"><i class="fas fa-sticky-note"></i> ملاحظات مهمة</h4>
            <div class="notes-content">
                <?php
                $notesContent = formatTourContent($tour['notes']);
                if (empty(trim(strip_tags($notesContent)))) {
                    // إذا لم يتم تنسيق الملاحظات، عرضها مع تنظيف الروابط
                    $cleanNotes = cleanMapLinks($tour['notes']);
                    $cleanNotes = addMapLinks($cleanNotes);
                    echo nl2br($cleanNotes);
                } else {
                    echo $notesContent;
                }
                ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- شريط الأدوات -->
        <div class="action-toolbar no-print mt-4 mb-4">
            <div class="row">
                <div class="col-md-8">
                    <div class="btn-group" role="group">
                        <button onclick="window.print()" class="btn btn-primary">
                            <i class="fas fa-print"></i> طباعة الرحلة
                        </button>
                        
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-download"></i> تصدير الرحلة
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="export_tour.php?id=<?= $tour['id'] ?>&format=pdf">
                                        <i class="fas fa-file-pdf"></i> تصدير PDF
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="export_tour.php?id=<?= $tour['id'] ?>&format=word">
                                        <i class="fas fa-file-word"></i> تصدير Word
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="export_tour.php?id=<?= $tour['id'] ?>&format=txt">
                                        <i class="fas fa-file-alt"></i> تصدير نص
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <a href="edit_tour.php?id=<?= $tour['id'] ?>" class="btn btn-secondary">
                            <i class="fas fa-edit"></i> تعديل الرحلة
                        </a>
                        
                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#shareModal">
                            <i class="fas fa-share-alt"></i> مشاركة
                        </button>
                    </div>
                </div>
                
                <div class="col-md-4 text-end">
                    <a href="my_tours.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للرحلات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal مشاركة الرحلة -->
    <div class="modal fade" id="shareModal" tabindex="-1" aria-labelledby="shareModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="shareModalLabel">مشاركة الرحلة</h5>
                    <button type="button" class="btn-close ms-0 me-auto" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">رابط الرحلة:</label>
                        <div class="input-group">
                            <input type="text" class="form-control" value="<?= htmlspecialchars("https://{$_SERVER['HTTP_HOST']}/tour.php?id={$tour['id']}") ?>" id="tourLink" readonly>
                            <button class="btn btn-outline-primary" onclick="copyToClipboard('tourLink')">
                                <i class="fas fa-copy"></i> نسخ
                            </button>
                        </div>
                    </div>
                    <div class="share-buttons text-center mt-4">
                        <a href="#" class="btn btn-primary mx-2"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="btn btn-info mx-2"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="btn btn-success mx-2"><i class="fab fa-whatsapp"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript لنسخ الرابط -->
    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            element.select();
            document.execCommand('copy');
            
            // إظهار رسالة نجاح
            const button = element.nextElementSibling;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
            setTimeout(() => {
                button.innerHTML = originalText;
            }, 2000);
        }
    </script>

    <!-- JavaScript مكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
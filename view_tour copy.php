<?php
require_once 'config.php';
require_once 'session_handler.php';
requireLogin();

// جلب معرف الرحلة
$tourId = $_GET['id'] ?? 0;

// جلب تفاصيل الرحلة
$stmt = $db->prepare("SELECT * FROM tour_plans WHERE id = ? AND user_id = ?");
$stmt->execute([$tourId, $_SESSION['user_id']]);
$tour = $stmt->fetch();

if (!$tour) {
    // توجيه في حال عدم وجود الرحلة
    header("Location: my_tours.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الرحلة</title>
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css">
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
            line-height: 1.8;
        }
        .tour-details {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        .day-header {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .activity-item {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="card">
            <div class="card-header">
                <h2 class="text-center">تفاصيل رحلة <?= htmlspecialchars($tour['city']) ?></h2>
            </div>
            <div class="card-body tour-details">
                <h3 class="mb-4">تفاصيل البرنامج</h3>
                
                <?php
                // طباعة المحتوى الكامل للتأكد من الظهور
                echo "<pre dir='rtl' style='white-space: pre-wrap; word-wrap: break-word; font-family: inherit;'>";
                echo htmlspecialchars($tour['plan_details']);
                echo "</pre>";

                // محاولة معالجة النص بشكل أكثر تفصيلاً
                $planDetails = $tour['plan_details'];

                // محاولة تقسيم النص باستخدام طرق مختلفة
                $dayPattern = '/\*\*اليوم \d+:\*\*/u';
                $days = preg_split($dayPattern, $planDetails);
                
                // إزالة العنصر الأول الفارغ
                array_shift($days);

                // استخراج عناوين الأيام
                preg_match_all($dayPattern, $planDetails, $dayTitles);

                // عرض محتوى الأيام بطريقة منظمة
                echo "<div class='days-container'>";
                foreach ($days as $index => $dayContent) {
                    echo "<div class='day-section mb-4'>";
                    
                    // عنوان اليوم
                    if (isset($dayTitles[0][$index])) {
                        echo "<div class='day-header'>";
                        echo "<h4>" . trim($dayTitles[0][$index]) . "</h4>";
                        echo "</div>";
                    }

                    // تقسيم النشاطات
                    $activities = preg_split('/\*\s*/u', $dayContent, -1, PREG_SPLIT_NO_EMPTY);

                    echo "<div class='activities-list'>";
                    foreach ($activities as $activity) {
                        if (trim($activity)) {
                            echo "<div class='activity-item'>";
                            echo "<p>" . trim($activity) . "</p>";
                            echo "</div>";
                        }
                    }
                    echo "</div>";
                    echo "</div>";
                }
                echo "</div>";

                // محاولة استخراج الملاحظات
                $notesPattern = '/\*\*ملحوظات:\*\*(.*)/us';
                if (preg_match($notesPattern, $planDetails, $matches)) {
                    echo "<div class='notes-section mt-4'>";
                    echo "<h4>ملاحظات هامة</h4>";
                    echo "<div class='alert alert-info'>" . nl2br(htmlspecialchars(trim($matches[1]))) . "</div>";
                    echo "</div>";
                }
                ?>
            </div>
            <div class="card-footer text-center">
                <a href="my_tours.php" class="btn btn-secondary me-2">رجوع للرحلات</a>
                <button onclick="window.print()" class="btn btn-primary">طباعة الرحلة</button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS (اختياري) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
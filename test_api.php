<?php
// test_api.php - صفحة اختبار Google AI API
session_start();

function testGoogleAI($prompt) {
    $apiKey = 'AIzaSyA7G2VbfTCoh392tSwmD8lNOPRwqJFBuCw';
    $url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent';
    
    $data = [
        'contents' => [
            ['parts' => [['text' => $prompt]]]
        ],
        'generationConfig' => [
            'temperature' => 0.7,
            'topK' => 40,
            'topP' => 0.95,
            'maxOutputTokens' => 1000
        ]
    ];

    // تهيئة طلب CURL
    $ch = curl_init($url . '?key=' . $apiKey);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

    // تنفيذ الطلب
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    
    curl_close($ch);

    return [
        'response' => $response,
        'httpCode' => $httpCode,
        'curlError' => $curlError,
        'decodedResponse' => json_decode($response, true)
    ];
}

$testResult = null;
$error = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $testPrompt = $_POST['prompt'] ?? 'اكتب لي برنامج سياحي بسيط لمدة يوم واحد في الرياض';
    
    try {
        $testResult = testGoogleAI($testPrompt);
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Google AI API</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        .result-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .error-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .success-box {
            background: #d1edff;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">اختبار Google AI API</h1>
        
        <div class="row">
            <div class="col-md-8 mx-auto">
                <form method="POST">
                    <div class="mb-3">
                        <label for="prompt" class="form-label">النص المراد إرساله للذكاء الاصطناعي:</label>
                        <textarea 
                            name="prompt" 
                            id="prompt" 
                            class="form-control" 
                            rows="4" 
                            placeholder="اكتب النص هنا..."
                        ><?= htmlspecialchars($_POST['prompt'] ?? 'اكتب لي برنامج سياحي بسيط لمدة يوم واحد في الرياض') ?></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">اختبار API</button>
                    <a href="preferences.php" class="btn btn-secondary">العودة للصفحة الرئيسية</a>
                </form>

                <?php if ($error): ?>
                    <div class="result-box error-box">
                        <h5>خطأ:</h5>
                        <pre><?= htmlspecialchars($error) ?></pre>
                    </div>
                <?php endif; ?>

                <?php if ($testResult): ?>
                    <div class="mt-4">
                        <h4>نتائج الاختبار:</h4>
                        
                        <div class="result-box">
                            <h5>رمز الاستجابة HTTP:</h5>
                            <span class="badge <?= $testResult['httpCode'] == 200 ? 'bg-success' : 'bg-danger' ?>">
                                <?= $testResult['httpCode'] ?>
                            </span>
                        </div>

                        <?php if ($testResult['curlError']): ?>
                            <div class="result-box error-box">
                                <h5>خطأ CURL:</h5>
                                <pre><?= htmlspecialchars($testResult['curlError']) ?></pre>
                            </div>
                        <?php endif; ?>

                        <div class="result-box">
                            <h5>الاستجابة الخام:</h5>
                            <pre><?= htmlspecialchars(substr($testResult['response'], 0, 1000)) ?><?= strlen($testResult['response']) > 1000 ? '...' : '' ?></pre>
                        </div>

                        <?php if ($testResult['decodedResponse']): ?>
                            <div class="result-box">
                                <h5>الاستجابة المحللة (JSON):</h5>
                                <pre><?= htmlspecialchars(json_encode($testResult['decodedResponse'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) ?></pre>
                            </div>

                            <?php if (isset($testResult['decodedResponse']['candidates'][0]['content']['parts'][0]['text'])): ?>
                                <div class="result-box success-box">
                                    <h5>النص المولد:</h5>
                                    <div><?= nl2br(htmlspecialchars($testResult['decodedResponse']['candidates'][0]['content']['parts'][0]['text'])) ?></div>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

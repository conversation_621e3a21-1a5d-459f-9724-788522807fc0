<?php
// تضمين ملفات الإعداد والجلسة
session_start();
require_once 'config.php';
require_once 'session_handler.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من وجود معرف الرحلة
$tourId = $_GET['id'] ?? null;
if (!$tourId) {
    die('لم يتم تحديد معرف الرحلة');
}

// استرجاع بيانات الرحلة من قاعدة البيانات
try {
    $stmt = $db->prepare("SELECT * FROM tour_plans WHERE id = ? AND user_id = ?");
    $stmt->execute([$tourId, $_SESSION['user_id']]);
    $tour = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$tour) {
        die('لم يتم العثور على الرحلة المطلوبة');
    }
} catch (PDOException $e) {
    die('خطأ في قاعدة البيانات: ' . $e->getMessage());
}

function formatTourContent($content) {
    $lines = explode("\n", $content);
    $formattedContent = '';
    $inList = false;

    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;

        // تنسيق عنوان اليوم - يدعم جميع الصيغ
        if(preg_match('/[\*]{0,2}(اليوم\s+(?:\d+|الأول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن|التاسع|العاشر):?)[\*]{0,2}/', $line)) {
            $formattedContent .= sprintf('<h3 class="day-title">%s</h3>', 
                htmlspecialchars(preg_replace('/[\*]/', '', $line))
            );
        }
        // تنسيق التكلفة والإقامة
        else if(preg_match('/[\*]{0,2}(تقدير.*(?:التكلفة|التكاليف)|التقديرات.*للتكلفة|الإقامة):?[\*]{0,2}/', $line)) {
            $formattedContent .= sprintf('<h4 class="cost-title">%s</h4>', 
                htmlspecialchars(preg_replace('/[\*]/', '', $line))
            );
        }
        // تنسيق الملاحظات
        else if(preg_match('/[\*]{0,2}(ملاحظ(?:ات|ة)|ملحوظ(?:ات|ة)):?[\*]{0,2}/', $line)) {
            $formattedContent .= sprintf('<div class="notes-section"><h4 class="notes-title">%s</h4>', 
                htmlspecialchars(preg_replace('/[\*]/', '', $line))
            );
        }
        // تنسيق النقاط
        else if(substr($line, 0, 1) === '*') {
            $formattedContent .= sprintf('<div class="bullet-point">%s</div>', 
                htmlspecialchars(substr($line, 1))
            );
        }
        // تنسيق الأوقات والأنشطة
        else if(preg_match('/\*\*(\d{2}:\d{2}\s+(?:صباحًا|مساءً|ظهرًا))\*\*:\s+(.*)/', $line, $matches)) {
            $formattedContent .= sprintf(
                '<div class="timeline-item">
                    <div class="timeline-time">%s</div>
                    <div class="timeline-content">%s</div>
                </div>',
                $matches[1],
                $matches[2]
            );
        }
        else {
            $formattedContent .= sprintf('<p class="regular-text">%s</p>', htmlspecialchars($line));
        }
    }

    return $formattedContent;
}

// Add new styles
?>
<style>
    .day-title {
        color: #2c3e50;
        font-size: 1.8em;
        margin: 30px 0 20px;
        padding: 15px;
        background: rgba(52, 152, 219, 0.1);
        border-radius: 8px;
        border-right: 4px solid #3498db;
    }

    .cost-title {
        color: #27ae60;
        font-size: 1.5em;
        margin: 25px 0 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid #27ae60;
    }

    .bullet-point {
        padding: 10px 30px;
        position: relative;
        font-size: 1.1em;
        line-height: 1.6;
    }

    .bullet-point:before {
        content: '•';
        color: #3498db;
        position: absolute;
        right: 10px;
        font-weight: bold;
    }

    .regular-text {
        font-size: 1.1em;
        line-height: 1.7;
        color: #2c3e50;
        margin: 10px 0;
    }
</style>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الرحلة - <?= htmlspecialchars($tour['city']) ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@500;700&display=swap">
    <style>
.arabic-content {
    direction: rtl;
    text-align: right;
    font-family: 'Tajawal', sans-serif;
    line-height: 1.5;
    font-size: 1.1em;
    font-weight: 500;
    padding: 20px;
   
}


        .tour-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            direction: rtl;
            font-family: 'Tajawal', sans-serif;
        }
        
        .tour-header {
            background-color: #f8f9fa;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        

    .location-link {
        display: inline-flex;
        align-items: center;
        font-size: 1.1em;
        color: #3498db;
        text-decoration: none;
        transition: all 0.3s;
        padding: 8px 15px;
        border-radius: 4px;
        background-color: #f8f9fa;
        margin: 0 5px;
    }

    .location-link i {
        color:rgb(224, 37, 16);  /* Red color for location icon */
        font-size: 1.2em;
        margin-left: 8px;
    }

    .timeline-content {
        background: #fff;
        padding: 15px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        font-size: 1.1em;  /* Increased font size */
        line-height: 1.6;
    }

    .timeline-time {
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 5px;
        font-size: 1.15em;  /* Increased time font size */
    }

    .tour-text {
        font-size: 1.1em;
        line-height: 1.7;
    }

        .tour-title {
            margin: 0;
            color: #2c3e50;
            font-size: 2em;
            margin-bottom: 10px;
        }

        .tour-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .meta-item {
            padding: 10px;
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .meta-label {
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }

        .timeline {
            list-style: none;
            padding: 0;
            position: relative;
        }
        
        .timeline:before {
            content: '';
            position: absolute;
            top: 0;
            right: 20px;
            height: 100%;
            width: 2px;
            background: #e9ecef;
        }
        
        .timeline-item {
            margin-bottom: 20px;
            position: relative;
            padding-right: 50px;
        }
        
        .timeline-item:before {
            content: '';
            position: absolute;
            right: 16px;
            top: 0;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #3498db;
            border: 2px solid #fff;
        }
        
        .timeline-time {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .timeline-content {
            background: #fff;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .tour-day-title {
            background-color: #f8f9fa;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            color: #2c3e50;
            font-size: 1.5em;
        }

        .location-link {
            display: inline-block;
            margin-right: 10px;
            color: #3498db;
            text-decoration: none;
            transition: all 0.3s;
            padding: 5px 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        
        .location-link:hover {
            color: #2980b9;
            background-color: #e9ecef;
            transform: translateY(-2px);
        }

        .notes-section {
            background: #f7f9fc;
            padding: 25px;
            border-radius: 15px;
            margin: 30px 0;
            border-right: 5px solid #3498db;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .notes-section h3 {
            color: #2c3e50;
            font-size: 1.5em;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .notes-content {
            color: #34495e;
            line-height: 1.8;
            font-size: 1.1em;
            padding: 10px;
        }

        .notes-section ul {
            padding-right: 20px;
            margin: 10px 0;
        }

        .notes-section li {
            margin-bottom: 10px;
            position: relative;
        }

        .notes-section li:before {
            content: '•';
            color: #3498db;
            font-weight: bold;
            margin-left: 10px;
        }

        .cost-section {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.3em;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }

        .additional-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .detail-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .detail-label {
            font-weight: bold;
            color: #3498db;
            margin-bottom: 8px;
            font-size: 1.1em;
        }

        .btn-primary {
            background-color: #3498db;
            border: none;
            padding: 8px 15px;
            transition: all 0.3s;
        }

        .btn-primary:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }

        .program-title {
            text-align: center;
            font-size: 1.8em;
            color: #2c3e50;
            background-color: rgba(52, 152, 219, 0.2); /* Light blue with 20% opacity */
            padding: 20px;
            margin: 30px auto;
            border-radius: 10px;
            font-weight: bold;
            max-width: 800px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
    </style>
</head>
<body>
    <div class="tour-container">
        <div class="tour-header">
            <h1 class="tour-title">تفاصيل رحلة <?= htmlspecialchars($tour['city']) ?></h1>
            <?php if(!empty($tour['map_url'])): ?>
                <a href="<?= htmlspecialchars($tour['map_url']) ?>" target="_blank" class="btn btn-primary mb-3">
                    <i class="fas fa-map-marker-alt"></i> عرض على الخريطة
                </a>
            <?php endif; ?>

            <!-- معلومات الرحلة الأساسية -->
            <div class="section-title">معلومات الرحلة الأساسية</div>
            <div class="tour-meta">
                <div class="meta-item">
                    <div class="meta-label">المدينة</div>
                    <?= htmlspecialchars($tour['city']) ?>
                </div>
                <div class="meta-item">
                    <div class="meta-label">المدة</div>
                    <?= $tour['days_count'] ?> أيام
                </div>
                <div class="meta-item">
                    <div class="meta-label">عدد المسافرين</div>
                    <?= $tour['travelers_count'] ?> أشخاص
                </div>
                <div class="meta-item">
                    <div class="meta-label">تاريخ الإنشاء</div>
                    <?= date('Y-m-d H:i:s', strtotime($tour['created_at'])) ?>
                </div>
            </div>

            <!-- تفاصيل إضافية -->
            <div class="section-title mt-4">تفاصيل إضافية</div>
            <div class="additional-details">
                <div class="detail-item">
                    <div class="detail-label">الإقامة</div>
                    <?= htmlspecialchars($tour['accommodation']) ?>
                </div>
                <div class="detail-item">
                    <div class="detail-label">المطاعم</div>
                    <?= htmlspecialchars($tour['restaurants']) ?>
                </div>
                <div class="detail-item">
                    <div class="detail-label">الأنشطة</div>
                    <?= htmlspecialchars($tour['activities']) ?>
                </div>
                <div class="detail-item">
                    <div class="detail-label">الميزانية التقديرية</div>
                    <?= number_format($tour['total_budget'], 2) ?> ريال
                </div>
            </div>
        </div>

        <div class="program-title">
            برنامج سياحي لمدة <?= $tour['days_count'] ?> أيام في <?= htmlspecialchars($tour['city']) ?>
        </div>

        <?php echo formatTourContent($tour['plan_details']); ?>
        
        <?php if(!empty($tour['notes'])): ?>
        <div class="notes-section">
            <h3>ملاحظات:</h3>
            <?php echo formatTourContent($tour['notes']); ?>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>

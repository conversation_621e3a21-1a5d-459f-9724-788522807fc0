<?php
require_once 'config.php';
require_once 'session_handler.php';
requireLogin();

// جلب معرف الرحلة
$tourId = $_GET['id'] ?? 0;

// جلب تفاصيل الرحلة
$stmt = $db->prepare("SELECT * FROM tour_plans WHERE id = ? AND user_id = ?");
$stmt->execute([$tourId, $_SESSION['user_id']]);
$tour = $stmt->fetch();

if (!$tour) {
    // توجيه في حال عدم وجود الرحلة
    header("Location: my_tours.php");
    exit();
}

// معالجة التعديل
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // تحديث بيانات الرحلة
        $stmt = $db->prepare("UPDATE tour_plans SET 
            city = ?, 
            days_count = ?, 
            travelers_count = ?, 
            accommodation = ?, 
            restaurants = ?, 
            activities = ?, 
            total_budget = ?, 
            plan_details = ?
            WHERE id = ?");
        
        $stmt->execute([
            $_POST['city'],
            $_POST['days_count'],
            $_POST['travelers_count'],
            $_POST['accommodation'],
            $_POST['restaurants'],
            $_POST['activities'],
            $_POST['total_budget'],
            $_POST['plan_details'],
            $tourId
        ]);

        // توجيه مع رسالة نجاح
        $_SESSION['success_message'] = 'تم تحديث الرحلة بنجاح';
        header("Location: view_tour.php?id=$tourId");
        exit();
    } catch(PDOException $e) {
        $error = "حدث خطأ: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <title>تعديل الرحلة</title>
        <!-- Bootstrap 5.3 CSS RTL -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f4f6f9;
        }
        .profile-container {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            padding: 30px;
            transition: all 0.3s ease;
        }
        .profile-container:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        .profile-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .profile-avatar {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            border: 5px solid #3498db;
        }
        .stats-card {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .stats-card:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .btn-edit-profile {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
    </style>
</head>


<style>
    .navbar {
        margin-bottom: 20px;
        background-color: #f8f9fa !important;
    }
    .nav-link {
        transition: all 0.3s ease;
    }
    .nav-link:hover {
        color: #3498db !important;
        transform: translateY(-2px);
    }
    .dropdown-menu {
        min-width: 200px;
        left: auto !important;
        right: 0 !important;
        margin-top: 10px;
        transform: translateX(-10px);
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        border: none;
        border-radius: 10px;
    }
</style>
</head>
<?php include 'navbar.php'; ?>

<!-- CSS إضافي -->
<style>
    .navbar {
        margin-bottom: 20px;
        background-color: #f8f9fa !important;
    }
    .nav-link {
        transition: all 0.3s ease;
    }
    .nav-link:hover {
        color: #3498db !important;
        transform: translateY(-2px);
    }
    .dropdown-menu {
        min-width: 200px;
    }
</style>
</head>
<body>
    <div class="container my-5">
        <h2>تعديل رحلة <?= htmlspecialchars($tour['city']) ?></h2>
        
        <?php if(isset($error)): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>

        <form method="POST">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label">المدينة</label>
                    <input type="text" name="city" class="form-control" 
                           value="<?= htmlspecialchars($tour['city']) ?>" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">عدد الأيام</label>
                    <input type="number" name="days_count" class="form-control" 
                           value="<?= $tour['days_count'] ?>" required>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label">عدد المسافرين</label>
                    <input type="number" name="travelers_count" class="form-control" 
                           value="<?= $tour['travelers_count'] ?>" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">الميزانية</label>
                    <input type="number" name="total_budget" class="form-control" 
                           value="<?= $tour['total_budget'] ?>" step="0.01" required>
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label">الإقامة</label>
                <input type="text" name="accommodation" class="form-control" 
                       value="<?= htmlspecialchars($tour['accommodation']) ?>">
            </div>

            <div class="mb-3">
                <label class="form-label">المطاعم</label>
                <input type="text" name="restaurants" class="form-control" 
                       value="<?= htmlspecialchars($tour['restaurants']) ?>">
            </div>

            <div class="mb-3">
                <label class="form-label">الأنشطة</label>
                <input type="text" name="activities" class="form-control" 
                       value="<?= htmlspecialchars($tour['activities']) ?>">
            </div>

            <div class="mb-3">
                <label class="form-label">تفاصيل البرنامج</label>
                <textarea name="plan_details" class="form-control" rows="10" required>
                    <?= htmlspecialchars($tour['plan_details']) ?>
                </textarea>
            </div>

            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-save"></i> حفظ التعديلات
                </button>
                <a href="view_tour.php?id=<?= $tour['id'] ?>" class="btn btn-secondary">
                    إلغاء
                </a>
            </div>
        </form>
    </div>
  <!-- Bootstrap JS (اختياري) -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- رابط أيقونات Bootstrap (اختياري) -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
</body>
</html>
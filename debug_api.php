<?php
// debug_api.php - تشخيص مشاكل API
header('Content-Type: text/html; charset=utf-8');

function debugGoogleAI() {
    $apiKey = 'AIzaSyA7G2VbfTCoh392tSwmD8lNOPRwqJFBuCw';
    $url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent';
    
    echo "<h2>تشخيص Google AI API</h2>";
    echo "<p><strong>API Key:</strong> " . substr($apiKey, 0, 10) . "..." . substr($apiKey, -5) . "</p>";
    echo "<p><strong>URL:</strong> $url</p>";
    
    // اختبار بسيط
    $testPrompt = "اكتب لي جملة واحدة عن السياحة";
    
    $data = [
        'contents' => [
            ['parts' => [['text' => $testPrompt]]]
        ],
        'generationConfig' => [
            'temperature' => 0.7,
            'maxOutputTokens' => 100
        ]
    ];
    
    echo "<h3>البيانات المرسلة:</h3>";
    echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    
    // إرسال الطلب
    $ch = curl_init($url . '?key=' . $apiKey);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_VERBOSE, true);
    
    // تسجيل معلومات CURL
    $verboseHandle = fopen('php://temp', 'w+');
    curl_setopt($ch, CURLOPT_STDERR, $verboseHandle);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    $curlInfo = curl_getinfo($ch);
    
    // قراءة معلومات CURL المفصلة
    rewind($verboseHandle);
    $verboseLog = stream_get_contents($verboseHandle);
    fclose($verboseHandle);
    
    curl_close($ch);
    
    echo "<h3>معلومات الطلب:</h3>";
    echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
    echo "<p><strong>CURL Error:</strong> " . ($curlError ?: 'لا يوجد') . "</p>";
    echo "<p><strong>Total Time:</strong> " . $curlInfo['total_time'] . " seconds</p>";
    
    echo "<h3>CURL Verbose Log:</h3>";
    echo "<pre>$verboseLog</pre>";
    
    echo "<h3>الاستجابة الخام:</h3>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    
    if ($response) {
        $decodedResponse = json_decode($response, true);
        if ($decodedResponse) {
            echo "<h3>الاستجابة المحللة:</h3>";
            echo "<pre>" . json_encode($decodedResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
            
            if (isset($decodedResponse['candidates'][0]['content']['parts'][0]['text'])) {
                echo "<h3>النص المولد:</h3>";
                echo "<div style='background: #e8f5e8; padding: 10px; border-radius: 5px;'>";
                echo nl2br(htmlspecialchars($decodedResponse['candidates'][0]['content']['parts'][0]['text']));
                echo "</div>";
            }
        } else {
            echo "<p style='color: red;'>فشل في تحليل JSON: " . json_last_error_msg() . "</p>";
        }
    }
}

// اختبار إعدادات PHP
echo "<h2>إعدادات PHP</h2>";
echo "<p><strong>cURL enabled:</strong> " . (extension_loaded('curl') ? 'نعم' : 'لا') . "</p>";
echo "<p><strong>OpenSSL enabled:</strong> " . (extension_loaded('openssl') ? 'نعم' : 'لا') . "</p>";
echo "<p><strong>allow_url_fopen:</strong> " . (ini_get('allow_url_fopen') ? 'نعم' : 'لا') . "</p>";
echo "<p><strong>max_execution_time:</strong> " . ini_get('max_execution_time') . " seconds</p>";

echo "<hr>";

// تشخيص API
debugGoogleAI();
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>تشخيص API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        h2, h3 { color: #333; }
    </style>
</head>
<body>
    <a href="preferences.php">العودة للصفحة الرئيسية</a>
</body>
</html>

<?php
require_once 'config.php';
require_once 'session_handler.php';
requireLogin();

// جلب معرف الرحلة
$tourId = $_GET['id'] ?? 0;

// جلب تفاصيل الرحلة
$stmt = $db->prepare("SELECT * FROM tour_plans WHERE id = ? AND user_id = ?");
$stmt->execute([$tourId, $_SESSION['user_id']]);
$tour = $stmt->fetch();

if (!$tour) {
    die("الرحلة غير موجودة");
}

// اختيار نوع التصدير
$format = $_GET['format'] ?? 'pdf';

switch($format) {
    case 'pdf':
        exportToPDF($tour);
        break;
    case 'word':
        exportToWord($tour);
        break;
    case 'txt':
        exportToText($tour);
        break;
    default:
        die("تنسيق غير مدعوم");
}

function exportToPDF($tour) {
    require_once 'vendor/autoload.php'; // استخدم TCPDF أو FPDF

    $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
    $pdf->SetCreator(PDF_CREATOR);
    $pdf->SetTitle('تفاصيل الرحلة');
    $pdf->SetHeaderData('', 0, 'تفاصيل الرحلة', '');
    $pdf->setHeaderFont(Array(PDF_FONT_NAME_MAIN, '', PDF_FONT_SIZE_MAIN));
    $pdf->setFooterFont(Array(PDF_FONT_NAME_DATA, '', PDF_FONT_SIZE_DATA));
    $pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
    $pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
    $pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
    $pdf->SetFooterMargin(PDF_MARGIN_FOOTER);
    $pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);

    $pdf->AddPage();
    $html = "
    <h1>تفاصيل الرحلة</h1>
    <p><strong>المدينة:</strong> {$tour['city']}</p>
    <p><strong>المدة:</strong> {$tour['days_count']} أيام</p>
    <p><strong>عدد المسافرين:</strong> {$tour['travelers_count']}</p>
    <h2>تفاصيل البرنامج</h2>
    <p>{$tour['plan_details']}</p>
    ";
    $pdf->writeHTML($html, true, false, true, false, '');
    $pdf->Output('tour_details.pdf', 'D');
}

function exportToWord($tour) {
    header('Content-Type: application/vnd.ms-word');
    header('Content-Disposition: attachment; filename="tour_details.doc"');

    echo "
    <html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word' xmlns='http://www.w3.org/TR/REC-html40'>
    <head><meta charset='utf-8'></head>
    <body>
        <h1>تفاصيل الرحلة</h1>
        <p><strong>المدينة:</strong> {$tour['city']}</p>
        <p><strong>المدة:</strong> {$tour['days_count']} أيام</p>
        <p><strong>عدد المسافرين:</strong> {$tour['travelers_count']}</p>
        <h2>تفاصيل البرنامج</h2>
        <p>{$tour['plan_details']}</p>
    </body>
    </html>";
    exit();
}

function exportToText($tour) {
    header('Content-Type: text/plain');
    header('Content-Disposition: attachment; filename="tour_details.txt"');

    echo "تفاصيل الرحلة\n";
    echo "-------------\n";
    echo "المدينة: {$tour['city']}\n";
    echo "المدة: {$tour['days_count']} أيام\n";
    echo "عدد المسافرين: {$tour['travelers_count']}\n\n";
    echo "تفاصيل البرنامج:\n";
    echo $tour['plan_details'];
    exit();
}
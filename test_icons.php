<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>اختبار أيقونات الموقع</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        .location-icon-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white !important;
            border-radius: 50%;
            text-decoration: none;
            margin: 0 5px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
            font-size: 12px;
        }

        .location-icon-link:hover {
            transform: scale(1.1) translateY(-2px);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
            background: linear-gradient(135deg, #c0392b, #a93226);
        }

        .location-icon-link i {
            color: white !important;
            margin: 0;
            font-size: 12px;
        }
        
        .timeline-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            border-right: 4px solid #3498db;
            box-shadow: 0 3px 15px rgba(0,0,0,0.08);
        }
        
        .timeline-time {
            font-weight: bold;
            color: #3498db;
            font-size: 1.1em;
            margin-bottom: 8px;
        }
        
        .timeline-activity {
            display: flex;
            align-items: flex-start;
            flex-wrap: wrap;
            gap: 8px;
            line-height: 1.6;
        }
        
        .activity-text {
            flex: 1;
            min-width: 0;
        }
        
        h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>اختبار أيقونات الموقع</h2>
        
        <h3>مثال 1: نص مع أماكن تركية</h3>
        <div class="timeline-item">
            <div class="timeline-time">09:00 صباحاً</div>
            <div class="timeline-activity">
                <span class="activity-text">
                    زيارة آيا صوفيا 
                    <a href="https://www.google.com/maps/search/Hagia+Sophia+Istanbul" class="location-icon-link" target="_blank" title="عرض آيا صوفيا على الخريطة">
                        <i class="fas fa-map-marker-alt"></i>
                    </a>
                    والتجول في المنطقة المحيطة
                </span>
            </div>
        </div>
        
        <h3>مثال 2: نص مع عدة أماكن</h3>
        <div class="timeline-item">
            <div class="timeline-time">14:00 ظهراً</div>
            <div class="timeline-activity">
                <span class="activity-text">
                    التسوق في البازار الكبير 
                    <a href="https://www.google.com/maps/search/Grand+Bazaar+Istanbul" class="location-icon-link" target="_blank" title="عرض البازار الكبير على الخريطة">
                        <i class="fas fa-map-marker-alt"></i>
                    </a>
                    ثم التوجه إلى برج غلطة 
                    <a href="https://www.google.com/maps/search/Galata+Tower+Istanbul" class="location-icon-link" target="_blank" title="عرض برج غلطة على الخريطة">
                        <i class="fas fa-map-marker-alt"></i>
                    </a>
                    لمشاهدة غروب الشمس
                </span>
            </div>
        </div>
        
        <h3>مثال 3: رابط خريطة مباشر</h3>
        <div class="timeline-item">
            <div class="timeline-time">19:00 مساءً</div>
            <div class="timeline-activity">
                <span class="activity-text">
                    العشاء في مطعم بمنطقة أورتاكوي 
                    <a href="https://www.google.com/maps/place/Ortak%C3%B6y+Mecidiye+Pier/@41.0500956,29.0316859,17z" class="location-icon-link" target="_blank" title="عرض على الخريطة">
                        <i class="fas fa-map-marker-alt"></i>
                    </a>
                    مع إطلالة على مضيق البوسفور
                </span>
            </div>
        </div>
        
        <h3>مثال 4: نص بدون روابط (سيتم إضافة أيقونات تلقائياً)</h3>
        <div class="timeline-item">
            <div class="timeline-time">10:30 صباحاً</div>
            <div class="timeline-activity">
                <span class="activity-text">
                    <?php
                    // محاكاة دالة addMapLinks
                    $text = "زيارة قصر توب كابي والتجول في حدائقه الجميلة";
                    
                    // إضافة أيقونة لقصر توب كابي
                    $text = str_replace(
                        'قصر توب كابي',
                        'قصر توب كابي <a href="https://www.google.com/maps/search/Topkapi+Palace+Istanbul" class="location-icon-link" target="_blank" title="عرض قصر توب كابي على الخريطة"><i class="fas fa-map-marker-alt"></i></a>',
                        $text
                    );
                    
                    echo $text;
                    ?>
                </span>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #e8f5e8; border-radius: 10px;">
            <h4>ملاحظات:</h4>
            <ul>
                <li>الأيقونات تظهر بجانب أسماء الأماكن</li>
                <li>عند الضغط على الأيقونة يتم فتح الموقع في خرائط جوجل</li>
                <li>الأيقونات لها تأثير hover جميل</li>
                <li>الروابط الطويلة يتم إخفاؤها وتظهر كأيقونات فقط</li>
            </ul>
        </div>
        
        <p style="text-align: center; margin-top: 30px;">
            <a href="tour_display.php?id=48" style="background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                عرض الصفحة الفعلية
            </a>
        </p>
    </div>
</body>
</html>

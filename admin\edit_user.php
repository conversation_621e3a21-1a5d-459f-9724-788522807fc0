<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once '../config.php';
require_once 'auth_check.php';

$userId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$error = '';
$success = '';

// Get user data
try {
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        header('Location: users.php');
        exit;
    }
} catch(PDOException $e) {
    $error = 'حدث خطأ في جلب بيانات المستخدم';
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $stmt = $db->prepare("
            UPDATE users 
            SET username = ?,
                email = ?,
                full_name = ?,
                phone = ?,
                city = ?,
                country = ?,
                interests = ?
            WHERE id = ?
        ");
        
        $stmt->execute([
            trim($_POST['username']),
            trim($_POST['email']),
            trim($_POST['full_name']),
            trim($_POST['phone']),
            trim($_POST['city']),
            trim($_POST['country']),
            trim($_POST['interests']),
            $userId
        ]);
        
        $success = 'تم تحديث بيانات المستخدم بنجاح';
    } catch(PDOException $e) {
        $error = 'حدث خطأ أثناء تحديث البيانات';
    }
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل بيانات المستخدم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/css/admin.css" rel="stylesheet">
</head>
<?php include '../navbar.php'; ?>
<body>
    <div class="d-flex">
        <?php include 'sidebar.php'; ?>
        <div class="main-content p-4 w-100">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>تعديل بيانات المستخدم</h2>
                <a href="users.php" class="btn btn-secondary">
                    <i class="bi bi-arrow-right me-1"></i>
                    عودة
                </a>
            </div>

            <?php if($success): ?>
                <div class="alert alert-success"><?= $success ?></div>
            <?php endif; ?>

            <?php if($error): ?>
                <div class="alert alert-danger"><?= $error ?></div>
            <?php endif; ?>

            <div class="card">
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" name="username" 
                                       value="<?= htmlspecialchars($user['username']) ?>" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" name="email" 
                                       value="<?= htmlspecialchars($user['email']) ?>" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" name="full_name" 
                                       value="<?= htmlspecialchars($user['full_name'] ?? '') ?>">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control" name="phone" 
                                       value="<?= htmlspecialchars($user['phone'] ?? '') ?>">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">المدينة</label>
                                <input type="text" class="form-control" name="city" 
                                       value="<?= htmlspecialchars($user['city'] ?? '') ?>">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الدولة</label>
                                <input type="text" class="form-control" name="country" 
                                       value="<?= htmlspecialchars($user['country'] ?? '') ?>">
                            </div>

                            <div class="col-12 mb-3">
                                <label class="form-label">الاهتمامات</label>
                                <textarea class="form-control" name="interests" rows="3"><?= htmlspecialchars($user['interests'] ?? '') ?></textarea>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check2 me-1"></i>
                                حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
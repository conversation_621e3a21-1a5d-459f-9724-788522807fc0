<?php
// تضمين ملفات الإعداد والجلسة
session_start();
require_once 'config.php';
require_once 'session_handler.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من وجود معرف الرحلة
$tourId = $_GET['id'] ?? null;
if (!$tourId) {
    die('لم يتم تحديد معرف الرحلة');
}

// استرجاع بيانات الرحلة من قاعدة البيانات
try {
    $stmt = $db->prepare("SELECT * FROM tour_plans WHERE id = ? AND user_id = ?");
    $stmt->execute([$tourId, $_SESSION['user_id']]);
    $tour = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$tour) {
        die('لم يتم العثور على الرحلة المطلوبة');
    }
} catch (PDOException $e) {
    die('خطأ في قاعدة البيانات: ' . $e->getMessage());
}

function formatTourContent($content) {
    $lines = explode("\n", $content);
    $formattedContent = '';
    $inList = false;

    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;

        // معالجة عناوين الأيام
        if (preg_match('/^اليوم (ال[أإا]ول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن|التاسع|العاشر|\d+)/', $line)) {
            if ($inList) {
                $formattedContent .= '</ul>';
            }
            $formattedContent .= sprintf('<h3 class="day-header">%s</h3><ul class="timeline">', $line);
            $inList = true;
            continue;
        }

        // معالجة الأنشطة والمواقع
        if (preg_match('/\*\*(\d{2}:\d{2}\s+(?:صباحًا|مساءً|ظهرًا))\*\*:\s+(.*)/', $line, $matches)) {
            $time = $matches[1];
            $activity = $matches[2];

            // إضافة رابط جوجل ماب إذا وُجد اسم مكان
            $locations = [
                'آيا صوفيا' => 'Hagia+Sophia',
                'المسجد الأزرق' => 'Blue+Mosque',
                'السوق الكبير' => 'Grand+Bazaar+Istanbul',
                'قصر توبكابي' => 'Topkapi+Palace'
            ];

            foreach ($locations as $place => $mapQuery) {
                if (stripos($activity, $place) !== false) {
                    $mapUrl = "https://www.google.com/maps/search/" . $mapQuery;
                    $activity = str_replace(
                        $place,
                        sprintf('<a href="%s" class="location-link" target="_blank">%s <i class="fa-solid fa-location-dot"></i></a>', $mapUrl, $place),
                        $activity
                    );
                }
            }

            $formattedContent .= sprintf(
                '<li class="timeline-item">
                    <div class="timeline-time">%s</div>
                    <div class="timeline-content">%s</div>
                </li>',
                $time,
                $activity
            );
            continue;
        }

        // النصوص العادية
        if (!$inList) {
            $formattedContent .= sprintf('<p class="tour-text">%s</p>', $line);
        } else {
            $formattedContent .= sprintf('<li class="timeline-item"><div class="timeline-content">%s</div></li>', $line);
        }
    }

    if ($inList) {
        $formattedContent .= '</ul>';
    }

    return $formattedContent;
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الرحلة - <?= htmlspecialchars($tour['city']) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Tajawal', sans-serif;
        }

        .tour-container {
            max-width: 1000px;
            margin: 30px auto;
            padding: 20px;
        }

        .tour-header {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .tour-title {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }

        .tour-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
        }

        .meta-item {
            text-align: center;
        }

        .meta-label {
            font-size: 0.9em;
            opacity: 0.9;
            margin-bottom: 5px;
        }

        .timeline {
            position: relative;
            padding: 20px 0;
        }

        .timeline::before {
            content: '';
            position: absolute;
            right: 20px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #3498db;
        }

        .timeline-item {
            margin-bottom: 30px;
            position: relative;
            padding-right: 50px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            right: 16px;
            top: 0;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #3498db;
            border: 2px solid #fff;
            box-shadow: 0 0 0 4px rgba(52,152,219,0.2);
        }

        .timeline-content {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .timeline-time {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .location-link {
            display: inline-flex;
            align-items: center;
            color: #3498db;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 20px;
            background-color: #f8f9fa;
            margin: 5px 0;
            transition: all 0.3s ease;
        }

        .location-link:hover {
            background-color: #3498db;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .location-link i {
            margin-right: 8px;
            color: #e74c3c;
        }

        .day-header {
            color: #2c3e50;
            font-size: 1.5em;
            margin: 30px 0 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        /* تنسيق العنوان الرئيسي */
        .tour-main-title {
            font-size: 2.2em;
            color: #2c3e50;
            text-align: center;
            margin: 30px 0;
            padding-bottom: 15px;
            border-bottom: 3px solid #3498db;
        }

        /* تنسيق عناوين الأيام */
        .day-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin: 25px 0;
            padding: 10px 20px;
            background: linear-gradient(to right, #3498db, transparent);
            border-radius: 8px;
            color: white;
        }

        /* تنسيق قسم التكاليف */
        .cost-section {
            background: #ffffff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
            margin: 30px 0;
        }

        .cost-title {
            color: #2c3e50;
            font-size: 1.5em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }

        .total-cost {
            font-size: 1.3em;
            color: #2980b9;
            font-weight: bold;
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        /* تنسيق قسم الملاحظات */
        .notes-section {
            background: #f7f9fc;
            padding: 25px;
            border-radius: 15px;
            border-right: 5px solid #3498db;
            margin: 30px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .notes-title {
            color: #2c3e50;
            font-size: 1.5em;
            margin-bottom: 15px;
        }

        .notes-content {
            color: #34495e;
            line-height: 1.8;
        }
    </style>
</head>
<body>
    <div class="tour-container">
        <div class="tour-header">
            <h1 class="tour-title">رحلة إلى <?= htmlspecialchars($tour['city']) ?></h1>
            <div class="tour-meta">
                <div class="meta-item">
                    <div class="meta-label">المدة</div>
                    <?= $tour['days_count'] ?> أيام
                </div>
                <div class="meta-item">
                    <div class="meta-label">عدد المسافرين</div>
                    <?= $tour['travelers_count'] ?> أشخاص
                </div>
                <div class="meta-item">
                    <div class="meta-label">الميزانية التقديرية</div>
                    <?= number_format($tour['total_budget']) ?> ريال
                </div>
                <div class="meta-item">
                    <div class="meta-label">تاريخ الإنشاء</div>
                    <?= date('Y/m/d', strtotime($tour['created_at'])) ?>
                </div>
            </div>
        </div>

        <div class="tour-main-title">برنامج سياحي لمدة يومين في دبي</div>

        <div class="day-title">اليوم 1:</div>
        <!-- محتوى اليوم الأول -->

        <div class="day-title">اليوم 2:</div>
        <!-- محتوى اليوم الثاني -->

        <div class="cost-section">
            <h3 class="cost-title">التقدير التكليفي (تقريبي):</h3>
            <!-- تفاصيل التكاليف -->
            <div class="total-cost">الإجمالي التقريبي: 1900 دولار أمريكي</div>
        </div>

        <div class="notes-section">
            <h3 class="notes-title">ملاحظات:</h3>
            <div class="notes-content">
                <!-- محتوى الملاحظات -->
            </div>
        </div>

        <?php echo formatTourContent($tour['plan_details']); ?>
    </div>
</body>
</html>

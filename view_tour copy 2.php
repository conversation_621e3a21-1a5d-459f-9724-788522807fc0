<?php
require_once 'config.php';
require_once 'session_handler.php';
requireLogin();

// التحقق من معرف الرحلة
$tourId = $_GET['id'] ?? 0;

// جلب تفاصيل الرحلة
$stmt = $db->prepare("SELECT * FROM tour_plans WHERE id = ? AND user_id = ?");
$stmt->execute([$tourId, $_SESSION['user_id']]);
$tour = $stmt->fetch();

if (!$tour) {
    // توجيه في حال عدم وجود الرحلة
    header("Location: my_tours.php");
    exit();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل رحلة <?= htmlspecialchars($tour['city']) ?></title>
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <style>
        @media print {
            .no-print {
                display: none;
            }
        }
        body {
            font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
            line-height: 1.8;
        }
        .tour-details {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        .day-header {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .activity-item {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .export-dropdown {
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="container my-5">
        <div class="card">
            <div class="card-header">
                <h3>تفاصيل رحلة <?= htmlspecialchars($tour['city']) ?></h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>معلومات الرحلة الأساسية</h5>
                        <ul class="list-unstyled">
                            <li><strong>المدينة:</strong> <?= htmlspecialchars($tour['city']) ?></li>
                            <li><strong>المدة:</strong> <?= $tour['days_count'] ?> أيام</li>
                            <li><strong>عدد المسافرين:</strong> <?= $tour['travelers_count'] ?></li>
                            <li><strong>تاريخ الإنشاء:</strong> <?= $tour['created_at'] ?></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>تفاصيل إضافية</h5>
                        <ul class="list-unstyled">
                            <li><strong>الإقامة:</strong> <?= htmlspecialchars($tour['accommodation']) ?></li>
                            <li><strong>المطاعم:</strong> <?= htmlspecialchars($tour['restaurants']) ?></li>
                            <li><strong>الأنشطة:</strong> <?= htmlspecialchars($tour['activities']) ?></li>
                            <li><strong>الميزانية:</strong> <?= number_format($tour['total_budget'], 2) ?> ريال</li>
                        </ul>
                    </div>
                </div>

                <hr>

                <div class="tour-details">
    <h5>تفاصيل البرنامج</h5>
    <div class="card">
        <div class="card-body">
            <?php
            // معالجة النص لعرضه بشكل صحيح
            $planDetails = $tour['plan_details'];

            // تشخيص أولي
            echo "طول النص: " . strlen($planDetails) . "";
            echo "<pre>محتوى النص الأصلي:\n" . htmlspecialchars($planDetails) . "</pre>";

            // التأكد من وجود محتوى
            if (empty(trim($planDetails))) {
                echo "<div class='alert alert-warning'>لا توجد تفاصيل متاحة للرحلة</div>";
                exit;
            }

            // دالة متقدمة للتحليل
            function parseItinerary($planDetails) {
                // أنماط البحث المتعددة
                $dayPatterns = [
                    '/\*\*اليوم \d+:\*\*/',
                    '/اليوم \d+:/',
                    '/Day \d+:/'
                ];

                $parsedDays = [];

                // محاولة التقسيم بأنماط مختلفة
                foreach ($dayPatterns as $pattern) {
                    $days = preg_split($pattern, $planDetails);
                    
                    if (count($days) > 1) {
                        array_shift($days); // إزالة العنصر الأول الفارغ
                        
                        preg_match_all($pattern, $planDetails, $dayTitles);

                        foreach ($days as $index => $dayContent) {
                            // تقسيم النشاطات
                            $activities = preg_split('/\*\s*/', $dayContent, -1, PREG_SPLIT_NO_EMPTY);
                            
                            $parsedDays[] = [
                                'title' => $dayTitles[0][$index] ?? "اليوم " . ($index + 1),
                                'activities' => $activities
                            ];
                        }

                        return $parsedDays;
                    }
                }

                return [];
            }

            // محاولة تحليل الخطة
            $parsedItinerary = parseItinerary($planDetails);

            // عرض النتائج
            if (!empty($parsedItinerary)) {
                foreach ($parsedItinerary as $day) {
                    echo "<div class='day-section'>";
                    echo "<div class='day-header'>";
                    echo "<h4>" . htmlspecialchars($day['title']) . "</h4>";
                    echo "</div>";

                    echo "<div class='activities-list'>";
                    foreach ($day['activities'] as $activity) {
                        echo "<div class='activity-item'>";
                        echo "<p>" . htmlspecialchars(trim($activity)) . "</p>";
                        echo "</div>";
                    }
                    echo "</div>";
                    echo "</div>";
                }
            } else {
                // محاولة عرض النص كامل إذا فشل التقسيم
                echo "<div class='alert alert-warning'>تعذر تحليل تفاصيل الرحلة</div>";
                echo "<pre>" . htmlspecialchars($planDetails) . "</pre>";
            }

            // معالجة الملاحظات
            $notesPattern = '/\*\*ملحوظات:\*\*(.*)/us';
            if (preg_match($notesPattern, $planDetails, $matches)) {
                echo "<div class='notes-section mt-4'>";
                echo "<h4>ملاحظات هامة</h4>";
                echo "<div class='alert alert-info'>" . nl2br(htmlspecialchars(trim($matches[1]))) . "</div>";
                echo "</div>";
            }
            ?>
        </div>
    </div>
</div>
            </div>

            <div class="card-footer no-print">
                <div class="d-flex justify-content-between">
                    <div>
                        <button onclick="window.print()" class="btn btn-primary">
                            <i class="bi bi-printer"></i> طباعة الرحلة
                        </button>
                        <a href="edit_tour.php?id=<?= $tour['id'] ?>" class="btn btn-secondary">
                            <i class="bi bi-pencil"></i> تعديل الرحلة
                        </a>
                    </div>
                    <a href="my_tours.php" class="btn btn-outline-secondary">
                        العودة للرحلات
                    </a>
                </div>
            </div>
        </div>

                   <!-- زر التصدير -->
                   <div class="dropdown export-dropdown no-print">
                <button class="btn btn-success dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-download"></i> تصدير الرحلة
                </button>
                <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                    <li>
                        <a class="dropdown-item" href="export_tour.php?id=<?= $tour['id'] ?>&format=pdf">
                            <i class="bi bi-file-pdf"></i> تصدير PDF
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="export_tour.php?id=<?= $tour['id'] ?>&format=word">
                            <i class="bi bi-file-word"></i> تصدير Word
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="export_tour.php?id=<?= $tour['id'] ?>&format=txt">
                            <i class="bi bi-file-text"></i> تصدير نص
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- تحسينات للطباعة -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تحسين عرض الطباعة
            window.addEventListener('beforeprint', function() {
                document.body.classList.add('print-mode');
            });

            window.addEventListener('afterprint', function() {
                document.body.classList.remove('print-mode');
            });
        });
    </script>
</body>
</html>
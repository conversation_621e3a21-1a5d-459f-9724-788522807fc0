<?php
// بدء الجلسة وتحميل الملفات الضرورية
session_start();
require_once 'config.php'; // ملف اتصال قاعدة البيانات
require_once 'session_handler.php'; // ملف إدارة الجلسات

// التحقق من تسجيل دخول المستخدم
requireLogin();

/**
 * دالة استدعاء واجهة برمجة التطبيقات الخاصة بالذكاء الاصطناعي
 * @param string $prompt النص المطلوب توليده
 * @return array الاستجابة من الذكاء الاصطناعي
 */
function callGoogleAI($prompt) {
    $apiKey = 'AIzaSyBLX9Pkgdbtm2Oitq7TTgZR7ONM9CMRgdQ';
    $url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
    
    $data = [
        'contents' => [
            ['parts' => [['text' => $prompt]]]
        ]
    ];

   // تهيئة طلب CURL
   $ch = curl_init($url . '?key=' . $apiKey);
   curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
   curl_setopt($ch, CURLOPT_POST, true);
   curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
   curl_setopt($ch, CURLOPT_HTTPHEADER, [
       'Content-Type: application/json',
       'Accept: application/json'
   ]);

   // تنفيذ الطلب
   $response = curl_exec($ch);
   
   // معالجة الأخطاء
   if ($response === false) {
       $error = curl_error($ch);
       curl_close($ch);
       throw new Exception("خطأ في الاتصال: " . $error);
   }

   curl_close($ch);

   // تحويل الاستجابة إلى مصفوفة
   return json_decode($response, true);
}

/**
* دالة حفظ خطة الرحلة في قاعدة البيانات
* @param PDO $db اتصال قاعدة البيانات
* @param array $tourData بيانات الرحلة الأساسية
* @param array $preferences تفضيلات المستخدم
* @param string $planDetails تفاصيل الخطة
* @return int|false معرف الرحلة المحفوظة
*/
function saveTourPlan($db, $tourData, $preferences, $planDetails) {
   try {
       // خريطة الميزانية
       $budgetMap = [
           'منخفضة' => 1500,
           'متوسطة' => 3500,
           'مرتفعة' => 7000,
           'فاخرة' => 15000
       ];

       // إعداد استعلام إدراج الرحلة
       $stmt = $db->prepare("INSERT INTO tour_plans (
           user_id, city, start_date, end_date, days_count, 
           travelers_count, accommodation, restaurants, 
           activities, total_budget, plan_details
       ) VALUES (
           :user_id, :city, :start_date, :end_date, :days_count, 
           :travelers_count, :accommodation, :restaurants, 
           :activities, :total_budget, :plan_details
       )");

       // ربط القيم
       $stmt->bindValue(':user_id', getCurrentUserId());
       $stmt->bindValue(':city', $tourData['city']);
       $stmt->bindValue(':start_date', date('Y-m-d'));
       $stmt->bindValue(':end_date', date('Y-m-d', strtotime("+{$preferences['days']} days")));
       $stmt->bindValue(':days_count', $preferences['days']);
       $stmt->bindValue(':travelers_count', $preferences['travelers_count'] ?? 1);
       
       // معالجة بيانات الإقامة
       $stmt->bindValue(':accommodation', 
           !empty($preferences['accommodation']) 
               ? implode(', ', $preferences['accommodation']) 
               : 'غير محدد'
       );
       
       // معالجة بيانات المطاعم
       $stmt->bindValue(':restaurants', 
           !empty($preferences['restaurants']) 
               ? implode(', ', $preferences['restaurants']) 
               : 'غير محدد'
       );
       
       // معالجة الأنشطة
       $stmt->bindValue(':activities', 
           !empty($preferences['activities']) 
               ? implode(', ', $preferences['activities']) 
               : 'غير محدد'
       );
       
       // تحديد الميزانية
       $stmt->bindValue(':total_budget', 
           $budgetMap[$preferences['budget'] ?? 'متوسطة']
       );
       
       // حفظ تفاصيل الخطة
       $stmt->bindValue(':plan_details', $planDetails);
       
       // تنفيذ الاستعلام
       $stmt->execute();
       
       // إرجاع معرف الرحلة المحفوظة
       return $db->lastInsertId();
   } catch(PDOException $e) {
       // تسجيل الخطأ
       error_log("خطأ في حفظ الرحلة: " . $e->getMessage());
       
       // إرسال رسالة خطأ
       setFlashMessage('error', 'حدث خطأ في حفظ الرحلة: ' . $e->getMessage());
       
       return false;
   }
}

// التحقق من وجود بيانات الرحلة
if (!isset($_SESSION['tour_data']) || !isset($_POST['days'])) {
   setFlashMessage('error', 'يرجى إدخال تفاصيل الرحلة أولاً');
   header("Location: preferences.php");
   exit();
}

// جمع بيانات الرحلة
$tourData = $_SESSION['tour_data'];
$preferences = $_POST;

// بناء البرومبت التفصيلي للذكاء الاصطناعي
$prompt = "أنا مسافر إلى {$tourData['city']} مع " . 
   ($preferences['travelers_count'] > 1 
       ? "{$preferences['travelers_count']} أشخاص" 
       : "نفسي"
   ) . " لمدة {$preferences['days']} أيام.\n";

// إضافة تفاصيل الإقامة
$prompt .= "الإقامة: " . implode(" أو ", $preferences['accommodation'] ?? []) . 
   (!empty($preferences['accommodation_view']) 
       ? " مع إطلالة " . implode(" أو ", $preferences['accommodation_view']) 
       : ""
   ) . 
   (!empty($preferences['accommodation_name']) 
       ? " في " . $preferences['accommodation_name'] 
       : "") . 
   ".\n";

// تفضيلات الطعام
$prompt .= "تفضيلاتي الغذائية:\n";
$prompt .= "- نوع الطعام: " . implode(" و ", $preferences['food_type'] ?? []) . "\n";
$prompt .= "- الوجبات المفضلة: " . implode(" و ", $preferences['meals'] ?? []) . "\n";

// الأنشطة والترفيه
$prompt .= "الأنشطة المطلوبة:\n";
$prompt .= "- أنشطة سياحية: " . implode(" و ", $preferences['activities'] ?? []) . "\n";
$prompt .= "- أماكن ترفيهية: " . implode(" و ", $preferences['entertainment'] ?? []) . "\n";

// وسائل التنقل
$prompt .= "وسائل النقل: " . implode(" و ", $preferences['transport'] ?? []) . 
    " مع سيارة " . ($preferences['car_type'] ?? 'اقتصادية') . ".\n";

// الميزانية
$prompt .= "الميزانية: {$preferences['budget']} مع تفضيلات إنفاق " . 
    implode(" و ", $preferences['spending_preferences'] ?? []) . ".\n";

// متطلبات خاصة
if (!empty($preferences['special_needs'])) {
    $prompt .= "متطلبات خاصة: " . implode(" و ", $preferences['special_needs']) . ".\n";
}

// ملاحظات إضافية
if (!empty($preferences['additional_notes'])) {
    $prompt .= "ملاحظات إضافية: {$preferences['additional_notes']}\n";
}

// التعليمات النهائية للذكاء الاصطناعي
// $prompt .= "\nأريدك أن تقوم بما يلي بشكل مفصل ودقيق:
// 1. إنشاء برنامج سياحي مفصل يومياً
// 2. تحديد الأوقات بدقة لكل نشاط
// 3. إضافة روابط Google Maps لكل معلم أو مطعم أو نشاط ومثال للرابط :
// 4. تقدير التكلفة التقريبية لكل نشاط والإقامة
// 5. مراعاة التفضيلات والمتطلبات الخاصة
// 6. ذكر أوقات الافتتاح والإغلاق والازدحام
// 7. إضافة التقييمات من الزوار
// 8. تقديم نبذة تعريفية عن كل معلم
// 9. التأكد من تناسب البرنامج مع عدد الأشخاص والميزانية";
// // التعليمات النهائية للذكاء الاصطناعي
// $prompt .= "\nتعليمات مهمة للروابط:
// 1. يجب إضافة رابط Google Maps دقيق لكل معلم سياحي
// 2. الرابط يجب أن يكون بصيغة مختصرة مثل:https://www.google.com/maps/place/Emin%C3%B6n%C3%BC+Bazaar/@41.0164873,28.9700046,17z
// 3. طريقة كتابة الرابط تكون على اسم المعلم بحيث يتم الضغط على المعلم ويتم الانتقال الى قوقل ماب بحيث يكون  الرابط تحت ايقونة \"<i class='fa-solid fa-location-dot'></i>\"
// 4. تأكد من دقة الموقع الجغرافي
// 5. فضّل الروابط المباشرة والمختصرة
// 6. تجنب الروابط الطويلة أو المعقدة
// 7. يفضل روابط Google Maps المختصرة";

// التعليمات النهائية للذكاء الاصطناعي
$prompt .= "\nأريدك أن تقوم بما يلي بشكل دقيق ومنظم:
1. إنشاء برنامج سياحي يومي مفصل.
   -اقتراح اسم الفندق  او المنتجع او الشقة
   -استبعد كلمة المعبد من البرنامج
   - تحديد الموقع الجغرافي للفندق او المنتجع او الشقة
   - تحديد تفاصيل الفندق او المنتجع او الشقة
   - العملة بحسب الدولة
   - اقتراح كوفيهات بين الفترات وخاصة في المساء قبيل الغروب
   - تجنب  اقتراح المشروبات الروحية والكحول والبارات
   - تحديد الأنشطة اليومية بالترتيب.
   - تخصيص أوقات دقيقة لكل نشاط وفق الجدول الزمني.
2. تقديم روابط Google Maps مختصرة لكل معلم أو مطعم أو نشاط.
   - مثال للرابط المختصر: https://www.google.com/maps/place/Emin%C3%B6n%C3%BC+Bazaar/@41.0164873,28.9700046,17z
   - يجب أن يكون الرابط السابق مدمجاً تحت أيقونة '<i class=\"fa-solid fa-location-dot\"></i>' وذلك كالتالي:
     <a href=\"https://www.google.com/maps/place/Emin%C3%B6n%C3%BC+Bazaar/@41.0164873,28.9700046,17z\" target=\"_blank\"><i class=\"fa-solid fa-location-dot\"></i></a>
3. تقدير التكلفة التقريبية لكل نشاط، بما في ذلك الإقامة والوجبات.
4. مراعاة التفضيلات الخاصة للمستخدمين، مثل عدد الأشخاص والميزانية.
5. إضافة أوقات العمل (الافتتاح والإغلاق) لكل معلم أو مطعم.
6. تقديم بيانات عن أوقات الازدحام لتجنب الفترات المزدحمة.
7. ذكر تقييمات الزوار بناءً على مصادر موثوقة.
8. تقديم نبذة تعريفية قصيرة ومميزة لكل معلم سياحي أو مطعم.
9. التأكد من تناسب البرنامج مع الميزانية وعدد الأشخاص.";

$prompt .= "\nتعليمات خاصة للروابط:
1. تأكد من دقة الروابط الجغرافية لكل موقع.
2. استخدم الروابط المختصرة لـ Google Maps، مثل: https://www.google.com/maps/place/Emin%C3%B6n%C3%BC+Bazaar/@41.0164873,28.9700046,17z
3. قم بتضمين الروابط تحت الأيقونة '<i class=\"fa-solid fa-location-dot\"></i>' بحيث يسهل الضغط عليها والانتقال إلى الموقع كالتالي:
   <a href=\"https://www.google.com/maps/place/Emin%C3%B6n%C3%BC+Bazaar/@41.0164873,28.9700046,17z\" target=\"_blank\"><i class=\"fa-solid fa-location-dot\"></i></a>
4. تجنب استخدام الروابط الطويلة أو غير الدقيقة.
5. راجع المواقع الجغرافية بدقة لتجنب أي خطأ.
6. روابط Google Maps يجب أن تكون مباشرة وقابلة للنقر.
7. لا تقم بتضمين النصوص داخل الروابط نفسها، بل استخدم الأيقونة فقط لتوضيح الرابط.";

$prompt .= "\nملاحظة: تأكد من أن الروابط تظهر بصيغة HTML وتكون قابلة للنقر من خلال إضافة الأيقونة المناسبة.";



try {
    // استدعاء الذكاء الاصطناعي
    $response = callGoogleAI($prompt);

    // التحقق من صحة الاستجابة
    if (!isset($response['candidates'][0]['content']['parts'][0]['text'])) {
        throw new Exception("استجابة غير صالحة من الذكاء الاصطناعي");
    }

    // استخراج تفاصيل الخطة
    $itinerary = $response['candidates'][0]['content']['parts'][0]['text'];
    
    // محاولة حفظ الخطة
    $planId = saveTourPlan($db, $tourData, $preferences, $itinerary);
    
    // التحقق من نجاح الحفظ
    if ($planId === false) {
        throw new Exception("فشل حفظ الرحلة");
    }

    // توجيه المستخدم لعرض الرحلة
    $_SESSION['current_plan_id'] = $planId;
    header("Location: tour_display.php?id={$planId}");
    exit();

} catch (Exception $e) {
    // تسجيل الخطأ
    error_log("خطأ في توليد الرحلة: " . $e->getMessage());
    
    // عرض رسالة خطأ
    setFlashMessage('error', 'حدث خطأ أثناء إنشاء الرحلة: ' . $e->getMessage());
    header("Location: preferences.php");
    exit();
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>توليد البرنامج السياحي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        .loading-spinner {
            width: 100px;
            height: 100px;
            border: 10px solid #f3f3f3;
            border-top: 10px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div class="container mt-5 text-center">
        <h2>جاري توليد البرنامج السياحي...</h2>
        <p>يرجى الانتظار، قد يستغرق الأمر بعض الوقت</p>
    </div>

    <script>
        // يمكن إضافة معالجات إضافية للانتظار
        window.addEventListener('load', function() {
            setTimeout(function() {
                // يمكن إضافة رسائل أو إجراءات إضافية
                console.log('جاري المعالجة...');
            }, 1000);
        });
    </script>
</body>
</html>
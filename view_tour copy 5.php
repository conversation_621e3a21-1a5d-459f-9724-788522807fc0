<?php
header('Content-Type: text/html; charset=utf-8');
mb_internal_encoding('UTF-8');
require_once 'config.php';
require_once 'session_handler.php';
requireLogin();

// التحقق من معرف الرحلة
$tourId = $_GET['id'] ?? 0;

// جلب تفاصيل الرحلة
$stmt = $db->prepare("SELECT * FROM tour_plans WHERE id = ? AND user_id = ?");
$stmt->execute([$tourId, $_SESSION['user_id']]);
$tour = $stmt->fetch();

if (!$tour) {
    // توجيه في حال عدم وجود الرحلة
    header("Location: my_tours.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الرحلة</title>
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@500;700&display=swap">

    <style>
        @media print {
            .no-print {
                display: none;
            }
        }
    </style>
 <style>
.arabic-content {
    direction: rtl;
    text-align: right;
    font-family: 'Tajawal', sans-serif;
    line-height: 1.5;
    font-size: 1.1em;
    font-weight: 500;
    padding: 20px;
   
}

.main-title {
    font-size: 1.52em;
    font-weight: 900;
    text-align: center;
    color: #000;
    margin: 20px 0;
    padding: 15px;
    background-color:rgb(214, 230, 247);
    border-radius: 8px;
    font-family: 'Tajawal', sans-serif;
}

.day-title, .cost-title, .note-title {
    font-size: 1.3em;
    font-weight: bold;
    color: #2c3e50;
    margin: 15px 0 10px 0;
    padding: 8px;
    background-color:rgb(229, 237, 248);
    border-radius: 5px;
}

.bullet-point {
    padding: 5px 25px 5px 0;
    position: relative;
    margin: 5px 0;
}

.bullet-point::before {
    content: "•";
    position: absolute;
    right: 8px;
    color: #2c3e50;
}

.regular-text {
    margin: 5px 0;
    padding: 0 10px;
}

.plan-text {
    white-space: pre-line;
    word-wrap: break-word;
}
</style>
</head>
<?php include 'navbar.php'; ?>
<body>
    <div class="container my-5">
        <div class="card">
            <div class="card-header">
                <h3>تفاصيل رحلة <?= htmlspecialchars($tour['city']) ?></h3>
                <a href="https://www.google.com/maps/place/Sumela+Monastery/@40.8795232,39.7937693,15z" target="_blank">
  <i class="fa-solid fa-location-dot"></i>
</a>

            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>معلومات الرحلة الأساسية</h5>
                        <ul class="list-unstyled">
                            <li><strong>المدينة:</strong> <?= htmlspecialchars($tour['city']) ?></li>
                            <li><strong>المدة:</strong> <?= $tour['days_count'] ?> أيام</li>
                            <li><strong>عدد المسافرين:</strong> <?= $tour['travelers_count'] ?></li>
                            <li><strong>تاريخ الإنشاء:</strong> <?= $tour['created_at'] ?></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>تفاصيل إضافية</h5>
                        <ul class="list-unstyled">
                            <li><strong>الإقامة:</strong> <?= htmlspecialchars($tour['accommodation']) ?></li>
                            <li><strong>المطاعم:</strong> <?= htmlspecialchars($tour['restaurants']) ?></li>
                            <li><strong>الأنشطة:</strong> <?= htmlspecialchars($tour['activities']) ?></li>
                            <li><strong>الميزانية التقديرية:</strong> <?= number_format($tour['total_budget'], 2) ?> 
                            ريال</li>
                        </ul>
                    </div>
                    
                </div>

                <hr>

                <div class="tour-details">
                <div class="tour-details">
                <h5>تفاصيل البرنامج</h5>
<div class="card">
    <div class="card-body arabic-content">
    <?php
$planDetails = $tour['plan_details'];
$lines = explode("\n", $planDetails);

echo '<div class="plan-text">';
foreach($lines as $line) {
    $line = trim($line);
    if($line !== '') {
        // تنسيق العنوان الرئيسي
        if(strpos($line, '##') === 0) {
            echo '<h2 class="main-title">' . htmlspecialchars(str_replace('##', '', $line)) . '</h2>';
        }
        // تنسيق عنوان اليوم - يدعم جميع الصيغ
        else if(preg_match('/[\*]{0,2}(اليوم\s+(?:\d+|الأول|الثاني|الثالث|الرابع|الخامس|السادس|السابع|الثامن|التاسع|العاشر):?)[\*]{0,2}/', $line)) {
            echo '<h4 class="day-title">' . htmlspecialchars(preg_replace('/[\*]/', '', $line)) . '</h4>';
        }
        // تنسيق التكلفة والإقامة - يدعم جميع الصيغ
        else if(preg_match('/[\*]{0,2}(تقدير.*(?:التكلفة|التكاليف)|التقديرات.*للتكلفة|الإقامة):?[\*]{0,2}/', $line)) {
            echo '<h4 class="cost-title">' . htmlspecialchars(preg_replace('/[\*]/', '', $line)) . '</h4>';
        }
        // تنسيق الملاحظات - يدعم جميع الصيغ
        else if(preg_match('/[\*]{0,2}(ملاحظ(?:ات|ة)|ملحوظ(?:ات|ة)):?[\*]{0,2}/', $line)) {
            echo '<h4 class="note-title">' . htmlspecialchars(preg_replace('/[\*]/', '', $line)) . '</h4>';
        }
        // تنسيق النقاط
        else if(substr($line, 0, 1) === '*') {
            echo '<div class="bullet-point">' . htmlspecialchars(substr($line, 1)) . '</div>';
        }
        // النص العادي
        else {
            echo '<p class="regular-text">' . htmlspecialchars($line) . '</p>';
        }
    }
}
echo '</div>';
?>
    </div>
</div>
</div>
</div>
            </div>

            <div class="card-footer no-print">
                <div class="d-flex justify-content-between">
                    <div>
                        <button onclick="window.print()" class="btn btn-primary">
                            <i class="bi bi-printer"></i> طباعة الرحلة
                        </button>
                        <a href="edit_tour.php?id=<?= $tour['id'] ?>" class="btn btn-secondary">
                            <i class="bi bi-pencil"></i> تعديل الرحلة
                        </a>
                    </div>
                    <a href="my_tours.php" class="btn btn-outline-secondary">
                        العودة للرحلات
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="dropdown no-print">
    <button class="btn btn-success dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="bi bi-download"></i> تصدير الرحلة
    </button>
    <ul class="dropdown-menu" aria-labelledby="exportDropdown">
        <li>
            <a class="dropdown-item" href="export_tour.php?id=<?= $tour['id'] ?>&format=pdf">
                <i class="bi bi-file-pdf"></i> تصدير PDF
            </a>
        </li>
        <li>
            <a class="dropdown-item" href="export_tour.php?id=<?= $tour['id'] ?>&format=word">
                <i class="bi bi-file-word"></i> تصدير Word
            </a>
        </li>
        <li>
            <a class="dropdown-item" href="export_tour.php?id=<?= $tour['id'] ?>&format=txt">
                <i class="bi bi-file-text"></i> تصدير نص
            </a>
        </li>
    </ul>
</div>


    <!-- Bootstrap JS (اختياري) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- رابط أيقونات Bootstrap (اختياري) -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
</body>
</html>
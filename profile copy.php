<?php
session_start();
require_once 'config.php';
require_once 'auth_middleware.php';
requireLogin();

function isProfileOwner() {
    return !isset($_GET['user_id']) || $_GET['user_id'] == $_SESSION['user_id'];
}

// Replace existing canViewFavorites function
function canViewFavorites($userId) {
    global $db;
    
    // Always show for own profile
    if ($userId == $_SESSION['user_id']) {
        return true;
    }
    
    try {
        $stmt = $db->prepare("SELECT favorites_privacy FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $privacy = $stmt->fetchColumn();
        
        error_log("User: $userId, Privacy Setting: $privacy");
        
        // Show if public
        return ($privacy == 1);
        
    } catch(PDOException $e) {
        error_log("Error checking privacy: " . $e->getMessage());
        return false;
    }
}

// Create uploads directory if it doesn't exist
$target_dir = "uploads/avatars/";
if (!file_exists($target_dir)) {
    mkdir($target_dir, 0777, true);
}

// Handle avatar upload
if(isset($_POST['update_avatar']) && isset($_FILES['avatar'])) {
    try {
        $file_extension = strtolower(pathinfo($_FILES["avatar"]["name"], PATHINFO_EXTENSION));
        $new_filename = "avatar_" . $_SESSION['user_id'] . "." . $file_extension;
        $target_file = $target_dir . $new_filename;
        
        if(move_uploaded_file($_FILES["avatar"]["tmp_name"], $target_file)) {
            $stmt = $db->prepare("UPDATE users SET avatar = ? WHERE id = ?");
            $stmt->execute([$new_filename, $_SESSION['user_id']]);
            $_SESSION['success_message'] = "تم تحديث الصورة الشخصية بنجاح";
            header("Location: profile.php");
            exit();
        }
    } catch(PDOException $e) {
        $_SESSION['error_message'] = "حدث خطأ أثناء تحديث الصورة الشخصية";
    }
}

// جلب معلومات المستخدم
// دالة للتشخيص
try {
    // استعلام محسن يتناسب مع بنية الجدول
    $toursStmt = $db->prepare("
        SELECT 
            COALESCE(COUNT(*), 0) as total_tours,
            COALESCE(SUM(days_count), 0) as total_days,
            COALESCE(COUNT(DISTINCT city), 0) as cities_visited
        FROM tour_plans 
        WHERE user_id = ?
    ");
    $toursStmt->execute([$_SESSION['user_id']]);
    $tourStats = $toursStmt->fetch(PDO::FETCH_ASSOC);



} catch(PDOException $e) {
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
}

try {
    // جلب معلومات المستخدم
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    // استعلام محسن
    $toursStmt = $db->prepare("
        SELECT 
            COALESCE(COUNT(*), 0) as total_tours,
            COALESCE(SUM(days_count), 0) as total_days,
            COALESCE(COUNT(DISTINCT country), 0) as countries_visited
        FROM tour_plans 
        WHERE user_id = ?
    ");
    $toursStmt->execute([$_SESSION['user_id']]);
    $tourStats = $toursStmt->fetch(PDO::FETCH_ASSOC);

    // تشخيص إضافي
    diagnoseToursTable($db, $_SESSION['user_id']);

} catch(PDOException $e) {
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
}

// Get user data and privacy settings
$profileUserId = isset($_GET['user_id']) ? $_GET['user_id'] : $_SESSION['user_id'];

try {
    // Get user data with privacy settings
    $stmt = $db->prepare("SELECT *, favorites_privacy FROM users WHERE id = ?");
    $stmt->execute([$profileUserId]);
    $profileUser = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Debug logging
    error_log("Profile User ID: " . $profileUserId);
    error_log("Session User ID: " . $_SESSION['user_id']);
    error_log("Privacy Setting: " . $profileUser['favorites_privacy']);
    
    // Check visibility permissions
    $canViewFavorites = ($profileUserId == $_SESSION['user_id']) || ($profileUser['favorites_privacy'] == 1);
    
    // Only fetch favorites if allowed
    $favorites = [];
    if ($canViewFavorites) {
        $favStmt = $db->prepare("SELECT * FROM favorites WHERE user_id = ? ORDER BY created_at DESC");
        $favStmt->execute([$profileUserId]);
        $favorites = $favStmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
} catch(PDOException $e) {
    error_log("Database Error: " . $e->getMessage());
    $canViewFavorites = false;
    $favorites = [];
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>الملف الشخصي</title>
    
    <!-- Bootstrap 5.3 CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f4f6f9;
        }
        .profile-container {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            padding: 30px;
            transition: all 0.3s ease;
        }
        .profile-container:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        .profile-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .profile-avatar {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            border: 5px solid #3498db;
        }
        .stats-card {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .stats-card:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .btn-edit-profile {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        .avatar-upload {
            position: relative;
            max-width: 150px;
            margin: 0 auto;
        }

        .avatar-edit {
            position: absolute;
            right: 5px;
            z-index: 1;
            bottom: 5px;
        }

        .avatar-edit input {
            display: none;
        }

        .avatar-edit label {
            display: inline-block;
            width: 34px;
            height: 34px;
            margin-bottom: 0;
            border-radius: 100%;
            background: #3498db;
            border: 1px solid transparent;
            box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.12);
            cursor: pointer;
            font-weight: normal;
            transition: all .2s ease-in-out;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .avatar-edit label:hover {
            background: #2980b9;
        }
    </style>
</head>


<style>
    .navbar {
        margin-bottom: 20px;
        background-color: #f8f9fa !important;
    }
    .nav-link {
        transition: all 0.3s ease;
    }
    .nav-link:hover {
        color: #3498db !important;
        transform: translateY(-2px);
    }
    .dropdown-menu {
        min-width: 200px;
        left: auto !important;
        right: 0 !important;
        margin-top: 10px;
        transform: translateX(-10px);
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        border: none;
        border-radius: 10px;
    }
</style>
<body>
    <!-- Navbar -->
    <?php include 'navbar.php'; ?>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="profile-container">
                    <!-- Replace the existing avatar HTML with this -->
                    <div class="profile-header">
                        <div class="avatar-upload">
                            <form action="" method="POST" enctype="multipart/form-data" id="avatarForm">
                                <div class="avatar-edit">
                                    <input type='file' name="avatar" id="imageUpload" accept=".png, .jpg, .jpeg" />
                                    <label for="imageUpload" class="bi bi-pencil-square"></label>
                                </div>
                                <div class="avatar-preview">
                                    <img src="<?= !empty($user['avatar']) ? 'uploads/avatars/' . htmlspecialchars($user['avatar']) : 'assets/default-avatar.png' ?>" 
                                        class="profile-avatar" id="imagePreview" alt="الصورة الشخصية">
                                </div>
                                <button type="submit" name="update_avatar" class="btn btn-primary btn-sm mt-2" style="display: none;" id="submitAvatar">
                                    حفظ الصورة
                                </button>
                            </form>
                        </div>
                    </div>

                    <h2><?= htmlspecialchars($user['full_name'] ?? $_SESSION['username']) ?></h2>
                    <p class="text-muted"><?= htmlspecialchars($user['email']) ?></p>
                    </div>

                    <div class="row mb-4">
    <div class="col-md-4">
        <div class="stats-card">
            <i class="bi bi-compass text-primary fs-2 mb-2"></i>
            <h5>الرحلات</h5>
            <p class="fs-4 fw-bold"><?= $tourStats['total_tours'] ?? 0 ?></p>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card">
            <i class="bi bi-calendar-check text-success fs-2 mb-2"></i>
            <h5>أيام السفر</h5>
            <p class="fs-4 fw-bold"><?= $tourStats['total_days'] ?? 0 ?></p>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card">
            <i class="bi bi-geo-alt text-danger fs-2 mb-2"></i>
            <h5>المدن</h5>
            <p class="fs-4 fw-bold"><?= $tourStats['cities_visited'] ?? 0 ?></p>
        </div>
    </div>
</div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="bi bi-person me-2"></i>معلومات شخصية
                                    </h5>
                                    <ul class="list-unstyled">
                                        <li><strong>اسم المستخدم:</strong> <?= htmlspecialchars($user['username']) ?></li>
                                        <li><strong>البريد الإلكتروني:</strong> <?= htmlspecialchars($user['email']) ?></li>
                                        <li><strong>تاريخ الانضمام:</strong> <?= date('Y-m-d', strtotime($user['created_at'])) ?></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="bi bi-gear me-2"></i>الإعدادات
                                    </h5>
                                    <div class="d-grid gap-2">
                                        <a href="edit_profile.php" class="btn btn-outline-primary btn-edit-profile">
                                            <i class="bi bi-pencil"></i> تعديل الملف الشخصي
                                        </a>
                                        <a href="change_password.php" class="btn btn-outline-secondary btn-edit-profile">
                                            <i class="bi bi-lock"></i> تغيير كلمة المرور
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<!-- Favorites Section -->

<div class="container mt-4">
    <h3 class="mb-4">
        <i class="bi bi-bookmark-heart-fill text-primary"></i>
        المفضلات السياحية
        <?php if($profileUserId == $_SESSION['user_id']): ?>
            <small>(<?= $profileUser['favorites_privacy'] == 1 ? 'عام' : 'خاص' ?>)</small>
        <?php endif; ?>
    </h3>
    
    <?php
    $showFavorites = true;
    if(isset($_GET['user_id']) && $_GET['user_id'] != $_SESSION['user_id']) {
        // Check if viewing another user's profile
        $stmt = $db->prepare("SELECT favorites_privacy FROM users WHERE id = ?");
        $stmt->execute([$_GET['user_id']]);
        $privacy = $stmt->fetchColumn();
        
        if($privacy == 0) {
            $showFavorites = false;
        }
    }

    if($showFavorites):
    ?>
    <div class="table-responsive">
        <table class="table table-hover border">
            <thead class="table-light">
                <tr>
                    <th>#</th>
                    <th>البرنامج السياحي</th>
                    <th>المدينة</th>
                    <th>التاريخ</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $stmt = $db->prepare("SELECT * FROM favorites WHERE user_id = ? ORDER BY created_at DESC");
                $stmt->execute([$_SESSION['user_id']]);
                $favorites = $stmt->fetchAll(PDO::FETCH_ASSOC);
                $counter = 1;
                
                foreach($favorites as $favorite): 
                    // استخراج السطر الأول كعنوان
                    $lines = explode("\n", $favorite['suggestion_text']);
                    $title = !empty($lines[0]) ? $lines[0] : 'برنامج سياحي';
                ?>
                <tr>
                    <td><?= $counter++ ?></td>
                    <td>
                        <a href="#" class="text-decoration-none" data-bs-toggle="collapse" 
                           data-bs-target="#favorite<?= $favorite['id'] ?>">
                            <?= htmlspecialchars($title) ?>
                        </a>
                    </td>
                    <td><?= htmlspecialchars($favorite['city_name']) ?></td>
                    <td><?= date('Y/m/d', strtotime($favorite['created_at'])) ?></td>
                    <td>
                        <button class="btn btn-sm btn-primary me-2" onclick="editFavorite(<?= $favorite['id'] ?>, '<?= htmlspecialchars($favorite['city_name']) ?>', `<?= htmlspecialchars($favorite['suggestion_text']) ?>`)">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteFavorite(<?= $favorite['id'] ?>)">
                            <i class="bi bi-trash"></i>
                        </button>
                    </td>
                </tr>
                <tr>
                    <td colspan="5" class="p-0">
                        <div id="favorite<?= $favorite['id'] ?>" class="collapse">
                            <div class="card card-body border-0">
                                <?= nl2br(htmlspecialchars($favorite['suggestion_text'])) ?>
                            </div>
                        </div>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <?php else: ?>
        <div class="alert alert-info">
            المفضلة خاصة ولا يمكن عرضها
        </div>
    <?php endif; ?>
</div>

<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل المفضلة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="editFavoriteId">
                <div class="mb-3">
                    <label class="form-label">المدينة</label>
                    <input type="text" class="form-control" id="editCity">
                </div>
                <div class="mb-3">
                    <label class="form-label">البرنامج السياحي</label>
                    <textarea class="form-control" id="editSuggestion" rows="10"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="saveFavoriteEdit()">حفظ التعديلات</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
            </div>
        </div>
    </div>
</div>

<script>
async function deleteFavorite(id) {
    if(confirm('هل أنت متأكد من حذف هذا البرنامج؟')) {
        try {
            const response = await fetch('delete_favorite.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ id: id })
            });
            
            const result = await response.json();
            if(result.success) {
                window.location.reload();
            } else {
                alert('حدث خطأ أثناء الحذف');
            }
        } catch(error) {
            console.error('Error:', error);
            alert('حدث خطأ أثناء الحذف');
        }
    }
}

function editFavorite(id, city, suggestion) {
    document.getElementById('editFavoriteId').value = id;
    document.getElementById('editCity').value = city;
    document.getElementById('editSuggestion').value = suggestion;
    new bootstrap.Modal(document.getElementById('editModal')).show();
}

async function saveFavoriteEdit() {
    const id = document.getElementById('editFavoriteId').value;
    const city = document.getElementById('editCity').value;
    const suggestion = document.getElementById('editSuggestion').value;

    try {
        const response = await fetch('edit_favorite.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ id, city, suggestion })
        });
        
        const result = await response.json();
        if(result.success) {
            window.location.reload();
        } else {
            alert('حدث خطأ أثناء التعديل');
        }
    } catch(error) {
        console.error('Error:', error);
        alert('حدث خطأ أثناء التعديل');
    }
}
</script>

<style>
.table th {
    background-color: #f8f9fa;
    font-weight: bold;
}
.table td {
    vertical-align: middle;
}
.collapse {
    background-color: #f8f9fa;
}
.card-body {
    white-space: pre-line;
}
</style>

    <!-- Bootstrap 5.3 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Add this JavaScript -->
    <script>
        document.getElementById('imageUpload').addEventListener('change', function() {
            const file = this.files[0];
            if(file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('imagePreview').src = e.target.result;
                    document.getElementById('submitAvatar').style.display = 'block';
                }
                reader.readAsDataURL(file);
            }
        });
    </script>
</body>
</html>
<?php
session_start();
require_once 'config.php';

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['error' => 'User not logged in']);
    exit;
}

$data = json_decode(file_get_contents('php://input'), true);
$id = $data['id'] ?? '';
$suggestion = $data['suggestion'] ?? '';
$city = $data['city'] ?? '';

if (empty($id)) {
    echo json_encode(['error' => 'Missing favorite ID']);
    exit;
}

try {
    $stmt = $db->prepare("UPDATE favorites SET suggestion_text = ?, city_name = ? WHERE id = ? AND user_id = ?");
    $stmt->execute([$suggestion, $city, $id, $_SESSION['user_id']]);
    echo json_encode(['success' => true]);
} catch(PDOException $e) {
    echo json_encode(['error' => $e->getMessage()]);
}
?>
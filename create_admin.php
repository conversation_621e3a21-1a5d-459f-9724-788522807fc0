<?php
require_once 'config.php';

try {
    $password = 'admin123';
    $hash = password_hash($password, PASSWORD_DEFAULT);
    
    $stmt = $db->prepare("INSERT INTO admin_users (username, password, email) VALUES (?, ?, ?)");
    $stmt->execute(['admin', $hash, '<EMAIL>']);
    
    echo "Admin created successfully\n";
    echo "Username: admin\n";
    echo "Password: admin123\n";
} catch(PDOException $e) {
    echo "Error: " . $e->getMessage();
}
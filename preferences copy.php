<?php
// preferences.php
session_start();
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $_SESSION['tour_data'] = $_POST;
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>تفضيلات الرحلة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container my-5">
        <h2 class="text-center mb-4">تفضيلات الرحلة</h2>
        
        <form action="generate_plan.php" method="POST" class="max-w-800 mx-auto">
            <div class="mb-3">
                <label class="form-label">عدد أيام الرحلة:</label>
                <select name="days" class="form-select" required>
                    <?php for($i=1; $i<=14; $i++) { ?>
                        <option value="<?php echo $i; ?>"><?php echo $i; ?> يوم</option>
                    <?php } ?>
                </select>
            </div>

            <div class="mb-3">
                <label class="form-label">نوع السكن:</label>
                <div class="form-check">
                    <input type="checkbox" name="accommodation[]" value="فندق 5 نجوم" class="form-check-input">
                    <label class="form-check-label">فندق 5 نجوم</label>
                </div>
                <div class="form-check">
                    <input type="checkbox" name="accommodation[]" value="فندق 4 نجوم" class="form-check-input">
                    <label class="form-check-label">فندق 4 نجوم</label>
                </div>
                <div class="form-check">
                    <input type="checkbox" name="accommodation[]" value="شقة فندقية" class="form-check-input">
                    <label class="form-check-label">شقة فندقية</label>
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label">المطاعم المفضلة:</label>
                <div class="form-check">
                    <input type="checkbox" name="restaurants[]" value="مطاعم محلية" class="form-check-input">
                    <label class="form-check-label">مطاعم محلية</label>
                </div>
                <div class="form-check">
                    <input type="checkbox" name="restaurants[]" value="مطاعم عالمية" class="form-check-input">
                    <label class="form-check-label">مطاعم عالمية</label>
                </div>
                <div class="form-check">
                    <input type="checkbox" name="restaurants[]" value="مطاعم عربية" class="form-check-input">
                    <label class="form-check-label">مطاعم عربية</label>
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label">الأنشطة المفضلة:</label>
                <div class="form-check">
                    <input type="checkbox" name="activities[]" value="تسوق" class="form-check-input">
                    <label class="form-check-label">تسوق</label>
                </div>
                <div class="form-check">
                    <input type="checkbox" name="activities[]" value="معالم سياحية" class="form-check-input">
                    <label class="form-check-label">معالم سياحية</label>
                </div>
                <div class="form-check">
                    <input type="checkbox" name="activities[]" value="مغامرات" class="form-check-input">
                    <label class="form-check-label">مغامرات</label>
                </div>
                <div class="form-check">
                    <input type="checkbox" name="activities[]" value="ترفيه" class="form-check-input">
                    <label class="form-check-label">ترفيه</label>
                </div>
            </div>

            <div class="text-center">
                <button type="submit" class="btn btn-primary">إنشاء البرنامج السياحي</button>
            </div>
        </form>
    </div>
</body>
</html>